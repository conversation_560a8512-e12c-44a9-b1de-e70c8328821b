import type { ApiResponse } from "./api";

export interface AccessToken {
  token: string;
  refresh_token: string;
  expired_at: number;
}

export interface KejaksaanUnit {
  id: number;
  name: string;
  unit: string; // "kejagung" | "kejati" | "kejari"
}

export interface UserAccess {
  can_accept_request: boolean;
  can_edit: boolean;
  can_input: boolean;
}

export interface LoginData {
  access_token: AccessToken;
  email: string;
  full_name: string;
  kejaksaan_unit: KejaksaanUnit;
  role: string;
  user_access: UserAccess;
  user_id: number;
}

export interface UserData {
  full_name: string;
  email: string;
  role: string;
  kejaksaan_unit: KejaksaanUnit;
  user_access: UserAccess;
  user_id: number;
}

export type LoginResponse = ApiResponse<LoginData>;
