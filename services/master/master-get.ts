import apiClient from "@/lib/axios/axios";
import {
  GetDistrictsResponse,
  GetEducationsResponse,
  GetJobsResponse,
  GetMaritalsResponse,
  GetProvincesResponse,
  GetRegenciesResponse,
  GetReligionsResponse,
  GetVillagesResponse,
} from "./master-get.interface";

export const getProvinces = async () => {
  const response = await apiClient.get<GetProvincesResponse>(
    "/master/provinces",
    {
      params: {
        page: 1,
        limit: 100,
      },
    },
    // {
    //   mockData: {
    //     status_code: 200,
    //     message: "successfully retrieved provinces",
    //     data: {
    //       total_data: 34,
    //       total_page: 4,
    //       current_page: 1,
    //       limit: 10,
    //       data: [
    //         {
    //           name: "SUMATERA SELATAN",
    //           id: 16,
    //         },
    //         {
    //           name: "SUMATERA BARAT",
    //           id: 13,
    //         },
    //         {
    //           name: "SUMATERA UTARA",
    //           id: 12,
    //         },
    //         {
    //           name: "RIAU",
    //           id: 14,
    //         },
    //         {
    //           name: "ACEH",
    //           id: 11,
    //         },
    //         {
    //           name: "BENGKULU",
    //           id: 17,
    //         },
    //         {
    //           name: "DKI JAKARTA",
    //           id: 31,
    //         },
    //         {
    //           name: "LAMPUNG",
    //           id: 18,
    //         },
    //         {
    //           name: "JAWA BARAT",
    //           id: 32,
    //         },
    //         {
    //           name: "JAMBI",
    //           id: 15,
    //         },
    //       ],
    //     },
    //     error: false,
    //     detail: {
    //       request_id: "a0e84cd6-00d8-4260-9a1b-6911345c7c57",
    //       query: "",
    //       path: "/api/v1/master/provinces",
    //       method: "GET",
    //       response_time: "2.360838ms",
    //       request_at: "2025-07-14T17:29:55+02:00",
    //       errors: null,
    //     },
    //   },
    // },
  );

  return response?.data;
};

export const getRegencies = async (provinceId: string) => {
  const response = await apiClient.get<GetRegenciesResponse>(
    `/master/regencies/${provinceId}`,
    {
      params: {
        page: 1,
        limit: 100,
      },
    },
    // {
    //   mockData:
    // {
    //   status_code: 200,
    //   message: "successfully retrieved regencies",
    //   data: {
    //     total_data: 11,
    //     total_page: 1,
    //     current_page: 1,
    //     limit: 100,
    //     data: [
    //       {
    //         name: "KABUPATEN KERINCI",
    //         province_id: 15,
    //         id: 1501,
    //       },
    //       {
    //         name: "KABUPATEN MERANGIN",
    //         province_id: 15,
    //         id: 1502,
    //       },
    //       {
    //         name: "KABUPATEN SAROLANGUN",
    //         province_id: 15,
    //         id: 1503,
    //       },
    //       {
    //         name: "KABUPATEN BATANG HARI",
    //         province_id: 15,
    //         id: 1504,
    //       },
    //       {
    //         name: "KABUPATEN MUARO JAMBI",
    //         province_id: 15,
    //         id: 1505,
    //       },
    //       {
    //         name: "KABUPATEN TANJUNG JABUNG TIMUR",
    //         province_id: 15,
    //         id: 1506,
    //       },
    //       {
    //         name: "KABUPATEN TANJUNG JABUNG BARAT",
    //         province_id: 15,
    //         id: 1507,
    //       },
    //       {
    //         name: "KABUPATEN TEBO",
    //         province_id: 15,
    //         id: 1508,
    //       },
    //       {
    //         name: "KABUPATEN BUNGO",
    //         province_id: 15,
    //         id: 1509,
    //       },
    //       {
    //         name: "KOTA JAMBI",
    //         province_id: 15,
    //         id: 1571,
    //       },
    //       {
    //         name: "KOTA SUNGAI PENUH",
    //         province_id: 15,
    //         id: 1572,
    //       },
    //     ],
    //   },
    //   error: false,
    //   detail: {
    //     request_id: "c93de0a9-b4a8-425f-8f32-4258b5236c07",
    //     query: "page=1\u0026limit=100",
    //     path: "/api/v1/master/regencies/15",
    //     method: "GET",
    //     response_time: "6.125668ms",
    //     request_at: "2025-07-14T17:51:31+02:00",
    //     errors: null,
    //   },
    // },
    // },
  );

  return response?.data;
};

export const getDistricts = async (regencyId: string) => {
  const response = await apiClient.get<GetDistrictsResponse>(
    `/master/districts/${regencyId}`,
    {
      params: {
        page: 1,
        limit: 100,
      },
    },
  );

  return response?.data;
};

export const getVillages = async (districtId: string) => {
  const response = await apiClient.get<GetVillagesResponse>(
    `/master/villages/${districtId}`,
    {
      params: {
        page: 1,
        limit: 100,
      },
    },
  );

  return response?.data;
};

export const getReligions = async () => {
  const response = await apiClient.get<GetReligionsResponse>(
    `/master/religions`,
    {
      params: {
        page: 1,
        limit: 100,
      },
    },
  );

  return response?.data;
};

export const getJobs = async () => {
  const response = await apiClient.get<GetJobsResponse>(`/master/jobs`, {
    params: {
      page: 1,
      limit: 100,
    },
  });

  return response?.data;
};

export const getMaritals = async () => {
  const response = await apiClient.get<GetMaritalsResponse>(`/master/maritals`);

  return response?.data;
};

export const getEducations = async () => {
  const response = await apiClient.get<GetEducationsResponse>(
    `/master/educations`,
    {
      params: {
        page: 1,
        limit: 100,
      },
    },
  );

  return response?.data;
};
