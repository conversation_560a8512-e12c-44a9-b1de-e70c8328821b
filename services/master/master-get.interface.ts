interface DefaultData {
  total_data: number;
  total_page: number;
  current_page: number;
  limit: number;
}

interface BasicData {
  name: string;
  id: number;
}

interface Regency extends BasicData {
  province_id: number;
}

interface District extends BasicData {
  regency_id: number;
}

interface Village extends BasicData {
  district_id: number;
}

interface ProvinceData extends DefaultData {
  data: BasicData[];
}

interface RegencyData extends DefaultData {
  data: Regency[];
}

interface DistrictData extends DefaultData {
  data: District[];
}

interface VillageData extends DefaultData {
  data: Village[];
}

export type GetVillagesResponse = VillageData;
export type GetDistrictsResponse = DistrictData;
export type GetRegenciesResponse = RegencyData;
export type GetProvincesResponse = ProvinceData;

export interface GetReligionsResponse extends DefaultData {
  data: BasicData[];
}

export interface GetJobsResponse extends DefaultData {
  data: BasicData[];
}

export interface GetMaritalsResponse extends DefaultData {
  data: BasicData[];
}

export interface GetEducationsResponse extends DefaultData {
  data: BasicData[];
}
