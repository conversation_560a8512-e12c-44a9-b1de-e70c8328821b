import apiClient from "@/lib/axios/axios";
import {
  GetListFormParams,
  GetListFormResponse,
  SuspectDetailData,
} from "./form-get.interface";

export const getListForm = async (params: GetListFormParams) => {
  const response = await apiClient.get<GetListFormResponse>(
    `/suspects/${params.unitID}`,
    {
      params: params.query,
      // mockData: {
      //   status_code: 200,
      //   message: "Form List fetched successfully",
      //   data: {
      //     total_data: 1,
      //     total_page: 1,
      //     current_page: 1,
      //     limit: 10,
      //     data: [
      //       {
      //         id: 6,
      //         name: "<PERSON><PERSON>",
      //         job: "dokter",
      //         umur: 27,
      //         alamat: "",
      //         pasal:
      //           "Tersangka diduga melanggar Pasal 372, 351(2) KUHP, Pasal 59(1) UU Psikotropika, serta Pasal 64 KUHP karena penggelapan, pengan<PERSON><PERSON><PERSON> berat, dan edar psikotropika tanpa izin secara berlanjut.",
      //         status_perkara: "DPO",
      //         jenis_perkara: "Pidana Umum",
      //         tanggal: "18 Juni 2025",
      //         catatan:
      //           "Tersangka diduga melakukan penggelapan dan penganiayaan berat terhadap korban.",
      //       },
      //       {
      //         id: 7,
      //         name: "Budi Santoso",
      //         job: "pengacara",
      //         umur: 35,
      //         alamat: "Jl. Merdeka No. 10, Jakarta",
      //         pasal:
      //           "Tersangka diduga melanggar Pasal 378 KUHP karena penipuan.",
      //         status_perkara: "Dalam Proses",
      //         jenis_perkara: "Pidana Umum",
      //         tanggal: "20 Juni 2025",
      //         catatan:
      //           "Tersangka diduga melakukan penipuan terhadap korban dengan modus investasi bodong.",
      //       },
      //     ],
      //   },
      //   error: false,
      //   detail: {
      //     request_id: "4c20112c-3fdf-4af5-ba46-930c9f31fce4",
      //     query: "",
      //     path: "/api/v1/form/list",
      //     method: "POST",
      //     response_time: "33.352542ms",
      //     request_at: "2025-06-26T02:37:33+07:00",
      //     errors: null,
      //   },
      // },
    },
  );

  return response?.data;
};

export const getSuspectDetail = async (
  id: number,
): Promise<SuspectDetailData | null> => {
  const response = await apiClient.get<SuspectDetailData>(
    `/suspect-detail/${id}`,
  );

  return response?.data;
};
