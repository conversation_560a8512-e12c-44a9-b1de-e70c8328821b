export interface GetListFormResponse {
  total_data: number;
  total_page: number;
  current_page: number;
  limit: number;
  data: ListFormData[];
}

export interface ListFormData {
  id: number;
  name: string;
  job: string;
  umur: number;
  alamat: string;
  pasal: string;
  status_perkara: string;
  jenis_perkara: string;
  tanggal: string;
  catatan: string;
}

export interface GetListFormParams {
  unitID: string | number;
  query?: {
    keyword?: string;
    status_perkara?: string;
    jenis_perkara?: string;
    asal_perkara?: string;
    limit?: number;
    page?: number;
    sort?: string;
  };
}

export interface SuspectDetailData {
  id: number;
  letter_code: string;
  ref_number: string;
  suspect_status: string;
  suspect_type: string;
  suspect_origin: string;
  kejaksaan_unit: KejaksaanUnitData;
  suspect_identity: SuspectIdentityData;
  suspect_history: SuspectHistoryData;
}
export interface KejaksaanUnitData {
  id: number;
  name: string;
  type: string;
}
export interface SuspectIdentityData {
  id: number;
  suspect_id: number;
  full_name: string;
  nickname: string;
  place_of_birth: string;
  date_of_birth: string;
  gender: string;
  citizenship: string;
  address: string;
  phone_numbers: string[] | null;
  identity_number: string;
  identity_type: string;
  religion: string;
  job: string;
  office_address: string;
  marital_status: string;
  organization_politic: string;
  education: string;
  is_recidivist: boolean;
  ethnic: string;
}
export interface SuspectHistoryData {
  id: number;
  suspect_id: number;
  article_violated: string;
  background_story: string;
  sp3_skkp: string;
  sp3_skkp_date: string;
  court_decision_pn: string;
  court_decision_ma: string;
  parent_name: string;
  parent_description: string;
  friend_name: string;
  other: string;
}
