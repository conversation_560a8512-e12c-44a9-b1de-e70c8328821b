import apiClient from "@/lib/axios/axios";
import {
  GetListKejaksaanTinggiParams,
  GetListKejaksaanTinggiResponse,
} from "./kejaksaan-tinggi.interface";

export const getListKejaksaanTinggi = async (
  params: GetListKejaksaanTinggiParams,
) => {
  const response = await apiClient.get<GetListKejaksaanTinggiResponse>(
    `/kejaksaan-units/`,
    {
      params: params,
      // mockData: {
      //   status_code: 200,
      //   message: "Kejaksaan Tinggi List fetched successfully",
      //   data: [
      //     {
      //       id: 1,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi DKI Jakarta",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //     {
      //       id: 2,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi Jawa Barat",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //     {
      //       id: 3,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi Jawa Tengah",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //     {
      //       id: 4,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi Jawa Timur",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //     {
      //       id: 5,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi Sumatera Utara",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //     {
      //       id: 6,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi Sumatera Selatan",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //     {
      //       id: 7,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi Sulawesi Selatan",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //     {
      //       id: 8,
      //       created_at: "2025-07-12T11:30:02.019806Z",
      //       updated_at: "2025-07-12T11:30:02.019806Z",
      //       deleted_at: null,
      //       name: "Kejaksaan Tinggi Kalimantan Selatan",
      //       type: "kejati",
      //       parent_id: null,
      //       Children: null,
      //     },
      //   ],
      //   error: false,
      //   detail: {
      //     request_id: "01c0e7a3-d607-4684-8fa7-4ad946ce6cdc",
      //     query: "",
      //     path: "/api/v1/kejaksaan-units/kejati/1",
      //     method: "GET",
      //     response_time: "5.5µs",
      //     request_at: "2025-07-13T16:19:33+02:00",
      //     errors: null,
      //   },
      // },
    },
  );
  // console.log("Kejaksaan Tinggi List Response:", response);
  return response?.data;
};
