export interface GetListKejaksaanTinggiResponse {
  total_data: number;
  total_page: number;
  current_page: number;
  limit: number;
  data: ListKejaksaanTinggiData[];
}

export interface ListKejaksaanTinggiData {
  id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  name: string;
  type: "kejagung" | "kejati" | "kejari";
  parent_id: number | null;
}

export interface GetListKejaksaanTinggiParams {
  page?: number;
  limit?: number;
  sort?: string;
  type?: "kejagung" | "kejati" | "kejari";
  parent_id?: number;
  search?: string;
}
