import apiClient from "@/lib/axios/axios";
import { GetChartDataResponse } from "./chart-get.interface";

export const getChartData = async () => {
  const response = await apiClient.get<GetChartDataResponse>(
    "/supect-cases-count",
    // {
    //   mockData: {
    //     status_code: 200,
    //     message: "success get suspect cases count",
    //     data: {
    //       pidum: {
    //         total: 2000,
    //         total_perstatus: {
    //           total_tersangka: 1000,
    //           total_terpidana: 600,
    //           total_terdakwa: 400,
    //           total_dpo: 300,
    //         },
    //         case_origin_percentage: {
    //           bareskrim: 100,
    //           polri: 200,
    //           polda: 300,
    //           polres: 75,
    //           polsek: 100,
    //           ppns: 40,
    //           kpk: 500,
    //           kejaksaan: 600,
    //         },
    //       },
    //       pidsus: {
    //         total: 900,
    //         total_perstatus: {
    //           total_tersangka: 100,
    //           total_terpidana: 200,
    //           total_terdakwa: 300,
    //           total_dpo: 400,
    //         },
    //         case_origin_percentage: {
    //           bareskrim: 100,
    //           polri: 200,
    //           polda: 150,
    //           polres: 50,
    //           polsek: 100,
    //           ppns: 300,
    //           kpk: 400,
    //           kejaksaan: 500,
    //         },
    //       },
    //     },
    //     error: false,
    //     detail: {
    //       request_id: "34b379ff-2e82-4342-a1b3-90a6c32bc89e",
    //       query: "",
    //       path: "/api/v1/supect-cases-count",
    //       method: "GET",
    //       response_time: "1.743µs",
    //       request_at: "2025-07-13T09:36:52+02:00",
    //       errors: null,
    //     },
    //   },
    // },
  );

  return response?.data;
};
