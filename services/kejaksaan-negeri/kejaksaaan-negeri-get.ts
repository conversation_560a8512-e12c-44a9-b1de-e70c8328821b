import apiClient from "@/lib/axios/axios";
import { GetListKejaksaanNegeriResponse } from "./kejaksaan-negeri.interface";

export const getListKejaksaanNegeri = async (parent_id: number) => {
  const response = await apiClient.get<GetListKejaksaanNegeriResponse[]>(
    `/kejaksaan-units/kejari/${parent_id}`,
    // {
    //   mockData: {
    //     status_code: 200,
    //     message: "Kejaksaan Negeri List fetched successfully",
    //     data: [
    //       {
    //         id: 10,
    //         name: "Kejaksaan Negeri Jakarta Pusat",
    //         unit: "Kejaksaan Negeri",
    //       },
    //       {
    //         id: 11,
    //         name: "Kejaksaan Negeri Jakarta Selatan",
    //         unit: "Kejaksaan Negeri",
    //       },
    //       {
    //         id: 13,
    //         name: "Kejaksaan Negeri Jakarta Barat",
    //         unit: "Kejaksaan Negeri",
    //       },
    //       {
    //         id: 12,
    //         name: "Kejaksaan <PERSON>ege<PERSON> Jakarta Timur",
    //         unit: "Kejaksaan Negeri",
    //       },
    //       {
    //         id: 14,
    //         name: "Kejaksaan Negeri Jakarta Utara",
    //         unit: "Kejaksaan Negeri",
    //       },
    //     ],

    //     error: false,
    //     detail: {
    //       request_id: "4c20112c-3fdf-4af5-ba46-930c9f31fce4",
    //       query: "",
    //     },
    //   },
    // },
  );
  return response.data;
};
