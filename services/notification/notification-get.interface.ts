export interface GetListNotificationResponse {
  total_unread: number;
  total_data: number;
  total_page: number;
  current_page: number;
  limit: number;
  data: NotificationData[];
}

export type NotificationStatusApproval = "approved" | "rejected" | "pending";

export interface NotificationData {
  id: number;
  is_read: boolean;
  name_tersangka: string;
  note: string;
  received_ago: string;
  requested_by: string;
  status: NotificationStatusApproval;
  suspect_id: number;
}
