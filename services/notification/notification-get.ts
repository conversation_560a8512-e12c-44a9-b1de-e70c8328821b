import apiClient from "@/lib/axios/axios";
import { GetListNotificationResponse } from "./notification-get.interface";

export const getListNotification = async () => {
  const response = await apiClient.get<GetListNotificationResponse>(
    "/notifications",
    // {
    //   mockData: {
    //     status_code: 200,
    //     message: "Notification fetched successfully",
    //     data: {
    //       total_unread: 1,
    //       total_data: 2,
    //       total_page: 1,
    //       current_page: 1,
    //       limit: 10,
    //       data: [
    //         {
    //           id: 1,
    //           is_read: false,
    //           name_tersangka: "<PERSON>",
    //           note: "This is a sample notification note.",
    //           received_ago: "2 hours ago",
    //           requested_by: "<PERSON>",
    //           status: "pending",
    //           suspect_id: 12345,
    //         },
    //         {
    //           id: 2,
    //           is_read: true,
    //           name_tersangka: "<PERSON>",
    //           note: "Another sample notification note.",
    //           received_ago: "1 day ago",
    //           requested_by: "<PERSON>",
    //           status: "approved",
    //           suspect_id: 67890,
    //         },
    //         {
    //           id: 3,
    //           is_read: false,
    //           name_tersangka: "<PERSON>",
    //           note: "Yet another sample notification note.",
    //           received_ago: "3 hours ago",
    //           requested_by: "Alice Johnson",
    //           status: "pending",
    //           suspect_id: 54321,
    //         },
    //         {
    //           id: 4,
    //           is_read: true,
    //           name_tersangka: "Charlie White",
    //           note: "Sample notification note for Charlie.",
    //           received_ago: "5 minutes ago",
    //           requested_by: "John Doe",
    //           status: "rejected",
    //           suspect_id: 98765,
    //         },
    //       ],
    //     },
    //     error: false,
    //     detail: {
    //       request_id: "4c20112c-3fdf-4af5-ba46-930c9f31fce4",
    //       query: "",
    //       path: "/api/v1/form/list",
    //       method: "POST",
    //       response_time: "33.352542ms",
    //       request_at: "2025-06-26T02:37:33+07:00",
    //       errors: null,
    //     },
    //   },
    // },
  );

  return response?.data;
};
