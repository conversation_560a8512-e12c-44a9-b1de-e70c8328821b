name: CI/CD - Build & Deploy on PR Merge to Main

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    environment: Production

    env:
      IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/kejagung_fe
      SERVER_USER: ${{ secrets.SERVER_USER }}
      SERVER_IP: ${{ secrets.SERVER_IP }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ env.IMAGE_NAME }}:latest
          build-args: |
            NEXT_PUBLIC_API_URL_V1=${{ secrets.NEXT_PUBLIC_API_URL_V1 }}

      - name: Set up SSH access
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known_hosts
        run: ssh-keyscan -H ${{ env.SERVER_IP }} >> ~/.ssh/known_hosts

      - name: Deploy to VPS
        env:
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
        run: |
          ssh ${{ env.SERVER_USER }}@${{ env.SERVER_IP }} <<'EOF'
            set -e
            cd /var/www/kejagung_fe

            echo "[1/4] 🔌 Stopping old containers..."
            docker compose down --remove-orphans

            echo "[2/4] 🔄 Logging in to Docker Hub..."
            echo "${{ secrets.DOCKERHUB_PASSWORD }}" | docker login -u "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin

            echo "[3/4] ⬇️ Pulling latest image..."
            docker compose pull

            echo "[4/4] 🚀 Starting container..."
            docker compose up -d --remove-orphans

            echo "🧹 Pruning unused images..."
            docker image prune -af
          EOF
