import { AxiosRequestConfig } from "axios";

export interface IApiDSCServices {
  agent?: string;
  customer?: string;
  authentication?: string;
  caseV2?: string;
}

export interface BaseConfigInterface {
  config?: AxiosRequestConfig;
  service?: keyof IApiDSCServices;
  customHeaders?: object;
  showLoader?: boolean;
  showPopupError?: boolean;
  isCustomerRequest?: boolean;
  popupCallbackError?: ICreateClient["popupCallbackError"];
}

export interface PopupCallbackError {
  title: string;
  description: string;
  type: "success" | "error" | "warning";
}

export interface ICreateClient {
  service?: keyof IApiDSCServices;
  customHeaders?: object;
  showLoader?: boolean;
  showPopupError?: boolean;
  params?: object;
  mockData?: object;
  popupCallbackError?: (errorCode: string) => PopupCallbackError | unknown;
}

export interface DSCBaseResponse<T> {
  transactionId: string;
  status: boolean;
  data: T;
  isArray: boolean;
  path: string;
  duration: string;
  method: string;
}

export interface ArrayDataTable<T> extends Omit<DSCBaseResponse<T>, "data"> {
  data: {
    page: number;
    pageSize: number;
    totalData: number;
    data: T[];
  }[];
}

export interface ArrayData<T> extends Omit<DSCBaseResponse<T>, "data"> {
  data: T;
}
