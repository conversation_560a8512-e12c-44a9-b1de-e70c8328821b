import axios, { AxiosInstance, AxiosResponse } from "axios";
import { getSession } from "next-auth/react";
import { Api<PERSON><PERSON>r<PERSON><PERSON><PERSON> } from "../error-handler";
import { BaseConfigInterface, ICreateClient } from "./axios-type";
import AxiosMockAdapter from "axios-mock-adapter";

const SECURITY_CONFIG = {
  allowedDomains: [process.env.NEXT_PUBLIC_API_URL_V1].filter(
    Boolean,
  ) as string[],
  // timeout: 10000, // 10 seconds
  // maxContentLength: 10 * 1024 * 1024,
  securityHeaders: {
    "X-Content-Type-Options": "nosniff",
  },
};

export const createClient = ({
  customHeaders,
  showLoader,
  showPopupError,
  params,
  mockData,
}: ICreateClient): AxiosInstance => {
  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL_V1,
    // timeout: SECURITY_CONFIG.timeout,
    // maxContentLength: SECURITY_CONFIG.maxContentLength,
    headers: {
      ...SECURITY_CONFIG.securityHeaders,
      Accept: "application/json",
      ...customHeaders,
    },
    params,
  });

  instance.interceptors.request.use(
    async (config) => {
      if (showLoader && !mockData) {
        // loader
      }
      const session = await getSession();
      if (session?.access_token.token) {
        config.headers.Authorization = `Bearer ${session.access_token.token}`;
      }

      if (!(config.data instanceof FormData)) {
        config.headers["Content-Type"] = "application/json";
      }

      return config;
    },
    (error) => Promise.reject(error),
  );

  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      const normalizedHeaders: Record<string, string> = {};
      Object.entries(response.headers).forEach(([key, value]) => {
        if (typeof value === "string") {
          normalizedHeaders[key.toLowerCase()] = value;
        }
      });
      return response.data;
    },
    (error) => {
      ApiErrorHandler({ error, showPopupError });
      return Promise.reject(error);
    },
  );

  return instance;
};

export const getInstance = async <T = unknown, R = AxiosResponse<T>>(
  url: string,
  options?: BaseConfigInterface & { params?: object; mockData?: object },
): Promise<R> => {
  const {
    config,
    service,
    customHeaders,
    showLoader,
    params,
    mockData,
    showPopupError,
  } = options ?? {};
  const client = createClient({
    service,
    customHeaders,
    showLoader,
    mockData,
    showPopupError,
  });

  if (mockData) {
    const mock = new AxiosMockAdapter(client);
    mock.onGet(url).reply(200, mockData);
  }
  return client.get<T, R>(url, {
    ...config,
    params,
  });
};

export const postInstance = async <T = unknown, R = AxiosResponse<T>>(
  url: string,
  options?: BaseConfigInterface & { data?: object },
): Promise<R> => {
  const { config, data, service, customHeaders, showLoader, showPopupError } =
    options ?? {};
  const client = createClient({
    service,
    customHeaders,
    showLoader,
    showPopupError,
  });
  return client.post<T, R>(url, data, config);
};

export const putInstance = async <T = unknown, R = AxiosResponse<T>>(
  url: string,
  options?: BaseConfigInterface & { data: T },
): Promise<R> => {
  const { config, data, service, customHeaders, showLoader, showPopupError } =
    options ?? {};
  const client = createClient({
    service,
    customHeaders,
    showLoader,
    showPopupError,
  });
  return client.put<T, R>(url, data, config);
};

export const deleteInstance = async <T = unknown, R = AxiosResponse<T>>(
  url: string,
  options?: BaseConfigInterface,
): Promise<R> => {
  const { config, service, customHeaders, showLoader, showPopupError } =
    options ?? {};
  const client = createClient({
    service,
    customHeaders,
    showLoader,
    showPopupError,
  });
  return client.delete<T, R>(url, config);
};

const apiClient = {
  get: getInstance,
  post: postInstance,
  put: putInstance,
  delete: deleteInstance,
};

export default apiClient;
