import { ButtonProps } from "@/components/ui/button/button-types";
import { PopupState } from "@/stores/popup/popup-interface";
import { AxiosError } from "axios";

export enum OtherErrorStatusCode {
  "_OFFLINE_" = "_OFFLINE_",
  "_TIMEOUT_" = "_TIMEOUT_",
  "_ECONNRESET_" = "_ECONNRESET_",
}

export interface ErrorResponse {
  message: string | string[];
  statusCode: number & OtherErrorStatusCode;
  transactionId?: string;
  status: boolean;
  timestamp: string;
  path: string;
}

export interface PopupCallbackError {
  title: string;
  description: string;
  actions: ButtonProps[];
  type: PopupState["type"];
}

export interface ApiErrorHandlerParams {
  error: AxiosError;
  showPopupError?: boolean;
  popupCallbackError?: (errorCode: string) => PopupCallbackError;
}
