import { usePopupStore } from "@/stores/popup/popup";
import { StatusCode } from "./error-handler.enum";
import { ApiErrorHandlerParams } from "./error-handler.interface";
import { signOut } from "@/utils/auth";

export const ApiErrorHandler = async ({
  error,
  showPopupError,
}: ApiErrorHandlerParams) => {
  const { openPopup } = usePopupStore.getState();

  switch (error.response?.status) {
    case StatusCode.Unauthorized:
      openPopup({
        title: "Unauthorized",
        message:
          "You are not authorized to perform this action. Waiting for redirect to login...",
        icon: undefined,
        actions: [],
      });

      localStorage.clear();
      setTimeout(async () => {
        await signOut();
      }, 1000);
      break;
    case StatusCode.InternalServerError:
      // noop
      // devs should handle this error by themselves.
      // otherwise HTTP 500 error types would fail silently
      break;
    case StatusCode.BadRequest: {
      break;
    }
    case StatusCode.UnprocessableEntity:
      break;
    case StatusCode.GatewayTimeout:
      if (showPopupError)
        openPopup({
          title: "Gateway Timeout",
          message:
            "The request took too long to process. Please try again later.",
          icon: undefined,
          actions: [],
        });
      break;
    default:
      if (showPopupError)
        openPopup({
          title: "Error",
          message: "An unexpected error occurred. Please try again later.",
          icon: undefined,
          actions: [],
        });
      break;
  }
};
