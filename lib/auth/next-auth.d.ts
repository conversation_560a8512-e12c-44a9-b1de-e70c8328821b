import { DefaultSession } from "next-auth";

declare module "next-auth" {
  export interface Session {
    user: {
      full_name: string;
      email: string;
      user_id: number;
      role: string;
      kejaksaan_unit: {
        name: string;
        id: number;
        unit: string;
      };
      user_access: {
        can_input: boolean;
        can_edit: boolean;
        can_accept_request: boolean;
      };
    } & DefaultSession["user"];
    access_token: {
      token: string;
      expired_at: number;
      refresh_token?: string;
    };
    expires: string;
  }

  export interface User {
    full_name: string;
    email: string;
    user_id: number;
    role: string;
    kejaksaan_unit: {
      name: string;
      id: number;
    };
    user_access: {
      can_input: boolean;
      can_edit: boolean;
      can_accept_request: boolean;
    };
    access_token: {
      token: string;
      expired_at: number;
      refresh_token?: string;
    };
  }
}

declare module "next-auth/jwt" {
  export interface JWT {
    user: {
      full_name: string;
      email: string;
      user_id: number;
      role: string;
      kejaksaan_unit: {
        name: string;
        id: number;
      };
      user_access: {
        can_input: boolean;
        can_edit: boolean;
        can_accept_request: boolean;
      };
    };
    access_token: {
      token: string;
      expired_at: number;
      refresh_token?: string;
    };
    expires: string;
  }
}
