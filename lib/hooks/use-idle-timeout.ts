"use client";

import { useCallback, useEffect, useRef, useState } from "react";

interface UseIdleTimeoutOptions {
  timeout: number; // in milliseconds
  onIdle?: () => void;
  onActive?: () => void;
  events?: string[];
  element?: Document | HTMLElement;
}

export const useIdleTimeout = ({
  timeout,
  onIdle,
  onActive,
  events = [
    "mousedown",
    "mousemove",
    "keypress",
    "scroll",
    "touchstart",
    "click",
  ],
  element = document,
}: UseIdleTimeoutOptions) => {
  const [isIdle, setIsIdle] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const eventListenerRef = useRef<(() => void) | null>(null);

  const resetTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (isIdle) {
      setIsIdle(false);
      onActive?.();
    }

    timeoutRef.current = setTimeout(() => {
      setIsIdle(true);
      onIdle?.();
    }, timeout);
  }, [timeout, onIdle, onActive, isIdle]);

  const handleActivity = useCallback(() => {
    resetTimer();
  }, [resetTimer]);

  useEffect(() => {
    // Set up event listeners
    events.forEach((event) => {
      element.addEventListener(event, handleActivity, true);
    });

    // Set initial timer
    resetTimer();

    // Store cleanup function
    eventListenerRef.current = () => {
      events.forEach((event) => {
        element.removeEventListener(event, handleActivity, true);
      });
    };

    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      eventListenerRef.current?.();
    };
  }, [events, element, handleActivity, resetTimer]);

  const pauseTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const resumeTimer = useCallback(() => {
    resetTimer();
  }, [resetTimer]);

  return {
    isIdle,
    pauseTimer,
    resumeTimer,
    resetTimer,
  };
};