# ========== BUILD STAGE ==========
ARG NODE_IMAGE=node:20-alpine

FROM ${NODE_IMAGE} AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

FROM ${NODE_IMAGE} AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ARG NEXT_PUBLIC_API_URL_V1
ENV NEXT_PUBLIC_API_URL_V1=$NEXT_PUBLIC_API_URL_V1

# Debug: List all files before build
RUN ls -la

ENV NEXT_TELEMETRY_DISABLED=1
RUN npm run build && npm prune --production

RUN ls -la /app

# ========== PRODUCTION STAGE ==========
FROM ${NODE_IMAGE} AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Copy production files with verification
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next

# Verify copied files
RUN ls -la && \
    ls -la public && \
    ls -la .next && \
    ls -la node_modules

# Security hardening
RUN apk add --no-cache dumb-init && \
    chown -R node:node /app

USER node

EXPOSE 3000
CMD ["npm", "start"]