import { create } from "zustand";
import { LoadingOverlayStore } from "./loading-overlay-interface";

export const useLoadingOverlayStore = create<LoadingOverlayStore>((set) => ({
  isVisible: false,
  message: "",
  show: (message: string = "", loaderComponent?: React.ReactNode) =>
    set({
      isVisible: true,
      message,
      loaderComponent,
    }),
  hide: () =>
    set({
      isVisible: false,
      message: "",
      loaderComponent: undefined,
    }),
}));
