import { create } from "zustand";
import { PopupState } from "./popup-interface";

export const usePopupStore = create<PopupState>((set) => ({
  isOpen: false,
  withHeader: true,
  size: "md",
  type: undefined,
  title: "",
  message: "",
  icon: undefined,
  classNameContainerActions: "",
  actions: [],
  openPopup: ({
    title,
    type,
    message,
    icon,
    withHeader = true,
    size = "md",
    actions,
    classNameContainerActions,
  }) =>
    set({
      isOpen: true,
      title,
      type,
      message,
      icon,
      withHeader,
      size,
      classNameContainerActions,
      actions: actions || [],
    }),
  closePopup: () =>
    set({
      isOpen: false,
      withHeader: true,
      size: "md",
      title: "",
      message: "",
      icon: undefined,
      classNameContainerActions: "",
      actions: [],
    }),
}));
