"use client";
import { ReactSelect } from "@/components/ui/react-select/react-select";
import { ChartPidanaUmum } from "./chart-pidana-umum";
import { ChartPidanaKhusus } from "./chart-pidana-khusus";
import { PageHeader } from "@/components/ui/page-header/page-header";
import { UserData } from "@/types/auth";
import { useState } from "react";
import { getListKejaksaanNegeri } from "@/services/kejaksaan-negeri/kejaksaaan-negeri-get";
import { useQuery } from "@tanstack/react-query";
import { getListKejaksaanTinggi } from "@/services/kejaksaan-tinggi/kejaksaan-tinggi-get";
import { getDropdownConfig, Unit } from "./config/dropdown.config";

import { TableSectionIntel } from "./table-section-intel";

export interface DashboardProps {
  userData?: UserData;
}

export const Dashboard = ({ userData }: DashboardProps) => {
  const [tableSection, setTableSection] = useState<boolean>(false);
  const [enableDropdown, setEnableDropdown] = useState<boolean>(false);
  const [kejaksaanTinggiID, setKejaksaanTinggiID] = useState<number>();
  const [kejaksaanUnitID, setKejaksaanUnitID] = useState<number>();

  const role = (userData?.kejaksaan_unit.unit as Unit) || "kejagung";

  const parentID = kejaksaanTinggiID
    ? kejaksaanTinggiID
    : userData?.kejaksaan_unit.id;

  const { data: kejaksaanNegeriList } = useQuery({
    queryKey: ["kejaksaanNegeriList", parentID],
    queryFn: () => getListKejaksaanNegeri(parentID || 0),
    enabled: !!kejaksaanTinggiID || role === "kejati", // Only run query if kejaksaanTinggiID exists and role is kejati
  });

  const { data: kejaksaanTinggiList } = useQuery({
    queryKey: ["kejaksaanTinggiList"],
    queryFn: () =>
      getListKejaksaanTinggi({
        type: "kejati",
      }),
  });

  const dropdownConfig = getDropdownConfig({
    kejaksaanTinggiList: kejaksaanTinggiList?.data,
    kejaksaanNegeriList: kejaksaanNegeriList,
    kejaksaanName: userData?.kejaksaan_unit.name || "Kejaksaan",
    setKejaksaanTinggiID: setKejaksaanTinggiID,
    setKejaksaanUnitID: setKejaksaanUnitID,
    enableDropdown,
  });

  const selectFilter = (dropdownConfig[role] || []).map((item) => ({
    element: (
      <ReactSelect
        options={item.options}
        placeholder={item.placeholder}
        isDisabled={item.disabled}
        onChange={(e) => {
          item.onClick?.(e?.value ? Number(e.value) : undefined);
          setEnableDropdown(true);
        }}
      />
    ),
  }));

  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        title="Filter Form"
        leftElement={[
          {
            element: (
              <div>
                <p className="text-sm text-gray-400">
                  Last Updated: 22/06/2023 14:30 WIB
                </p>
                <h1 className="text-2xl font-bold">Dashboard Analitik</h1>
              </div>
            ),
          },
        ]}
        rightElement={[
          ...selectFilter,
          {
            label: "Lihat Data",
            onClick: () => setTableSection(true),
            isButton: true,
            variant: "primary",
          },
        ]}
      />
      <ChartPidanaUmum />
      <ChartPidanaKhusus />
      {tableSection && (
        <TableSectionIntel
          kejaksaanUnitID={kejaksaanUnitID}
          userData={userData}
        />
      )}
    </div>
  );
};
