"use client";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/intel";
import { getChartData } from "@/services/chart/chart-get";
import { useQuery } from "@tanstack/react-query";

export const ChartPidanaKhusus = () => {
  const { data: chartData } = useQuery({
    queryKey: ["chartData"],
    queryFn: () => {
      return getChartData();
    },
  });
  const dataDoughnutChart = {
    total: chartData?.pidsus.total || 0,
    labels: [
      "Bareskrim",
      "Polri",
      "Polda",
      "Polres",
      "Polsek",
      "PPNS",
      "KPK",
      "Kejaksaan",
    ],
    datasets: [
      {
        data: [
          chartData?.pidsus?.case_origin_percentage.bareskrim || 0,
          chartData?.pidsus?.case_origin_percentage.polri || 0,
          chartData?.pidsus?.case_origin_percentage.polda || 0,
          chartData?.pidsus?.case_origin_percentage.polres || 0,
          chartData?.pidsus?.case_origin_percentage.polsek || 0,
          chartData?.pidsus?.case_origin_percentage.ppns || 0,
          chartData?.pidsus?.case_origin_percentage.kpk || 0,
          chartData?.pidsus?.case_origin_percentage.kejaksaan || 0,
        ],
        backgroundColor: [
          "#E82127",
          "#A36027",
          "#FFC107",
          "#FFF176",
          "#8BC34A",
          "#4CAF50",
          "#2196F3",
          "#9C27B0",
        ],
        borderWidth: 0,
      },
    ],
  };

  const dataBarChart = {
    total: chartData?.pidsus.total || 0,
    labels: ["Tersangka", "Terdakwa", "Terpidana", "DPO"],
    datasets: [
      {
        label: "Jumlah Perkara",
        data: [
          chartData?.pidsus.total_perstatus.total_tersangka ?? 0,
          chartData?.pidsus.total_perstatus.total_terdakwa ?? 0,
          chartData?.pidsus.total_perstatus.total_terpidana ?? 0,
          chartData?.pidsus.total_perstatus.total_dpo ?? 0,
        ],
        backgroundColor: [
          "rgba(243, 199, 62, 1)",
          "rgba(243, 131, 62, 1)",
          "rgba(240, 68, 56, 1)",
          "rgba(168, 35, 35, 1)",
        ],
        borderColor: [
          "rgba(243,199,62, 1)",
          "rgba(255, 99, 132, 1)",
          "rgba(75, 192, 192, 1)",
          "rgba(255, 159, 64, 1)",
        ],
        borderWidth: 1,
        borderRadius: 5,
      },
    ],
  };
  return (
    <div className="border-primary-300 rounded-lg border p-4">
      <h2 className="text-primary-300 mb-4 text-2xl font-bold">
        Pidana Khusus
      </h2>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <BarChart data={dataBarChart} />
        <DoughnutChart data={dataDoughnutChart} />
      </div>
    </div>
  );
};
