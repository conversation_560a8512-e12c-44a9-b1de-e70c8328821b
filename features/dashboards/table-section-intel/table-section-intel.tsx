"use client";
import { SearchInput } from "@/components/ui";
import { PageHeader } from "@/components/ui/page-header/page-header";
import {
  DROPDOWN_STATUS_PERKARA,
  DROPDOWN_ASAL_PERKARA,
  DROPDOWN_JENIS_PERKARA,
} from "@/constants";
import { TableSection } from "@/features/home/<USER>";
import { UserData } from "@/types/auth";
import { useCallback, useMemo, useState } from "react";
import { ReactSelect } from "@/components/ui/react-select/react-select";
import { routes } from "@/lib/routes";
import debounce from "lodash/debounce";

interface TableSectionIntelProps {
  kejaksaanUnitID?: number;
  userData?: UserData;
}

export const TableSectionIntel: React.FC<TableSectionIntelProps> = ({
  kejaksaanUnitID,
  userData,
}) => {
  const [keyword, setKeyword] = useState("");
  const [query, setQuery] = useState({
    asal_perkara: "",
    jenis_perkara: "",
    status_perkara: "",
    keyword: "",
  });

  const handleSearchChange = useMemo(
    () =>
      debounce((value: string) => {
        setQuery((prev) => ({
          ...prev,
          keyword: value,
        }));
      }, 300),
    [],
  );

  const handleSelectChange = useCallback(
    (field: keyof typeof query, value: string) => {
      setQuery((prev) => ({
        ...prev,
        [field]: value,
      }));
    },
    [],
  );

  const unitID = kejaksaanUnitID ?? userData?.kejaksaan_unit?.id ?? 0;

  return (
    <div className="flex flex-col space-y-4">
      <PageHeader
        title="Filter Form"
        leftElement={[
          {
            element: (
              <SearchInput
                placeholder="Cari berdasarkan NAMA/NIK/TTL"
                value={keyword}
                onChange={(e) => {
                  const value = e.target.value;
                  setKeyword(value);
                  handleSearchChange(value);
                }}
              />
            ),
          },
        ]}
        rightElement={[
          {
            element: (
              <ReactSelect
                options={DROPDOWN_STATUS_PERKARA}
                isClearable
                placeholder="Status Perkara"
                onChange={(e) =>
                  handleSelectChange("status_perkara", e?.value || "")
                }
              />
            ),
          },
          {
            element: (
              <ReactSelect
                options={DROPDOWN_ASAL_PERKARA}
                isClearable
                placeholder="Asal Perkara"
                onChange={(e) =>
                  handleSelectChange("asal_perkara", e?.value || "")
                }
              />
            ),
          },
          {
            element: (
              <ReactSelect
                options={DROPDOWN_JENIS_PERKARA}
                isClearable
                placeholder="Jenis Perkara"
                onChange={(e) =>
                  handleSelectChange("jenis_perkara", e?.value || "")
                }
              />
            ),
          },
          {
            label: "Lihat Semua Data",
            href: routes.managements.form.index,
            isButton: true,
            variant: "primary",
          },
        ]}
      />

      <TableSection
        params={{
          unitID,
          query: {
            ...query,
            keyword: query.keyword.trim(),
          },
        }}
      />
    </div>
  );
};
