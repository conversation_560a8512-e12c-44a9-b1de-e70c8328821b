"use client";
import { Table } from "@/components/ui/table";
import { columns } from "../table";
import { useQuery } from "@tanstack/react-query";
import { getListForm } from "@/services/form/form-get";

export interface TableSectionProps {
  params: {
    unitID: number;
    query?: {
      keyword?: string;
      status_perkara?: string;
      jenis_perkara?: string;
      asal_perkara?: string;
      limit?: number;
      page?: number;
      sort?: string;
    };
  };
}

export default function TableSection(props: TableSectionProps) {
  const {
    data: listForm,
    isLoading: isLoadingListForm,
    // isError: isErrorListForm,
  } = useQuery({
    queryKey: ["listForm", { ...props.params }],
    queryFn: ({ queryKey }) => {
      const [, params] = queryKey;
      if (typeof params === "string") {
        throw new Error("Invalid params type");
      }
      return getListForm(params);
    },
  });

  return (
    <Table
      data={listForm?.data ?? []}
      columns={columns}
      pageCount={listForm?.total_page}
      isLoading={isLoadingListForm}
    />
  );
}
