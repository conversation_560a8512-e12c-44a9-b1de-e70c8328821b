"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "flowbite-react";
import { ListFormData } from "@/services/form/form-get.interface";
import { HiOutlinePencil, HiOutlineEye } from "react-icons/hi";

const columns: ColumnDef<ListFormData>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "umur",
    header: "Umur",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "alamat",
    header: "Alamat",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "pasal",
    header: () => (
      <span className="inline-block w-full text-center">Pasal</span>
    ),
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "status_perkara",
    header: () => (
      <span className="inline-block w-full text-center">Status Perkara</span>
    ),
    cell: (info) =>
      info.getValue() == "dpo" ? (
        <Badge color="failure" className="mx-auto w-fit rounded-2xl">
          {info.getValue() as string}
        </Badge>
      ) : info.getValue() == "terdakwa" ? (
        <Badge color="pink" className="mx-auto w-fit rounded-2xl">
          {info.getValue() as string}
        </Badge>
      ) : info.getValue() == "terpidana" ? (
        <Badge color="failure" className="mx-auto w-fit rounded-2xl">
          {info.getValue() as string}
        </Badge>
      ) : (
        <Badge color="warning" className="mx-auto w-fit rounded-2xl">
          {info.getValue() as string}
        </Badge>
      ),
  },
  {
    accessorKey: "jenis_perkara",
    header: () => (
      <span className="inline-block w-full text-center">Jenis Perkara</span>
    ),
    cell: (info) => (
      <Badge className="mx-auto w-fit rounded-2xl">
        {info.getValue() as string}
      </Badge>
    ),
  },
  {
    accessorKey: "asal_perkara",
    header: () => (
      <span className="inline-block w-full text-center">Asal Perkara</span>
    ),
    cell: (info) => (
      <Badge className="mx-auto w-fit">{info.getValue() as string}</Badge>
    ),
  },
  {
    accessorKey: "tanggal",
    header: "Tanggal Request",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "catatan",
    header: "Catatan",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "action",
    header: "Aksi",
    cell: () => (
      <div className="flex items-center justify-center space-x-4">
        <button className="text-neutral-14 hover:text-neutral-11">
          <HiOutlinePencil className="h-5 w-5" />
        </button>
        <button className="text-neutral-14 hover:text-neutral-11">
          <HiOutlineEye className="h-5 w-5" />
        </button>
      </div>
    ),
  },
];

export default columns;
