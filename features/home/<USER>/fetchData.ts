import { TData } from "../types";

const fetchData = async (): Promise<TData[]> => {
  // const URL = process.env.APP_URL || ""

  // const res = await fetch(URL);
  // const data = await res.json();

  // return data;

  return [
    {
      name: "<PERSON><PERSON>",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "<PERSON><PERSON>",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "<PERSON><PERSON>",
      job: "<PERSON>kter",
      age: 27,
      address: "Jl. <PERSON>mata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "Alena Schleif",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "Alena Schleif",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "Alena Schleif",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "Alena Schleif",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "Alena Schleif",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "Alena Schleif",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
    {
      name: "Alena Schleif",
      job: "Dokter",
      age: 27,
      address: "Jl. Permata Indah III No. 46",
      clauses: "Pelanggaran Berat",
      caseStatus: "DPO",
      status: "Accepted",
      type: "PIDSUS",
      dateTime: "18 Juni 2025",
      notes: "-",
    },
  ];
}

export default fetchData;