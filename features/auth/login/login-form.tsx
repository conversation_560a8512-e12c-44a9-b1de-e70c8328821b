"use client";
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from "flowbite-react";
import Link from "next/link";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import z from "zod";
import { routes } from "@/lib/routes";
import { signIn } from "next-auth/react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { FormInput } from "@/components/ui/form-input";
import { FaEye, FaEyeSlash } from "react-icons/fa";

export interface FormLoginValue {
  email: string;
  password: string;
}

export const LoginForm = () => {
  const router = useRouter();

  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const formLogin = useForm<FormLoginValue>({
    mode: "onSubmit",
    resolver: zodResolver(
      z.object({
        email: z.string().email("Email is not valid"),
        password: z.string().nonempty("Password is required"),
      }),
    ),
  });

  const {
    handleSubmit,
    formState: { errors },
  } = formLogin;
  const onSubmit = async (data: FormLoginValue) => {
    const { email, password } = data;
    setLoading(true);
    const result = await signIn("credentials", {
      email,
      password,
      redirect: false,
    });

    if (result?.ok) {
      router.push(routes.dashboard);
    } else {
      setError(result?.error ?? "");
    }
    setLoading(false);
  };

  return (
    <FormProvider {...formLogin}>
      <form
        className="flex max-w-md flex-col space-y-5"
        onSubmit={handleSubmit(onSubmit)}
      >
        {error && (
          <Alert color="failure" onDismiss={() => setError(null)}>
            {error}
          </Alert>
        )}
        <div className="flex flex-col space-y-3">
          <FormInput
            placeholder="Masukkan email anda"
            label="Email"
            id="email"
            name="email"
            inputType="text"
            sizing="md"
            isInvalid={!!errors.email}
            errorMessage={errors.email?.message}
          />
          <FormInput
            placeholder="Masukkan password anda"
            label="Password"
            name="password"
            id="password"
            inputType="text"
            sizing="md"
            type={showPassword ? "text" : "password"}
            isInvalid={!!errors.password}
            errorMessage={errors.password?.message}
            rightElement={
              <Button
                type="button"
                color="alternative"
                className="focous:outline-0 cursor-pointer border-0 bg-transparent hover:bg-transparent focus:ring-0"
                onClick={() => {
                  setShowPassword((prev) => !prev);
                }}
              >
                {showPassword ? (
                  <FaEye className="h-5 w-5" />
                ) : (
                  <FaEyeSlash className="h-5 w-5" />
                )}
              </Button>
            }
          />
        </div>
        <div className="flex justify-end gap-2">
          <Link
            href={routes.forgotPassword}
            className="text-primary-300 text-sm font-semibold underline"
          >
            Lupa Kata Sandi?
          </Link>
        </div>
        <Button
          type="submit"
          className="py-6 font-bold"
          size="md"
          color={"primary"}
          disabled={loading}
        >
          {loading && <Spinner light className="mr-2 h-4 w-4" />}
          Masuk
        </Button>
      </form>
    </FormProvider>
  );
};
