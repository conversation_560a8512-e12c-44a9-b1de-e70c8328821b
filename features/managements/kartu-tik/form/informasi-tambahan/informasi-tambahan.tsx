import { FormInput } from "@/components/ui/form-input";
import React from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";
import { useQuery } from "@tanstack/react-query";
import {
  getEducations,
  getJobs,
  getMaritals,
  getReligions,
} from "@/services/master/master-get";

export const InformasiTambahan = () => {
  const {
    formState: { errors },
  } = useFormContext<FormValues>();

  const { data: dataReligion, isLoading: isLoadingReligion } = useQuery({
    queryKey: ["getReligions"],
    queryFn: () => getReligions(),
  });

  const { data: dataJob, isLoading: isLoadingJob } = useQuery({
    queryKey: ["getJobs"],
    queryFn: () => getJobs(),
  });

  const { data: dataMaritals, isLoading: isLoadingMaritals } = useQuery({
    queryKey: ["getMaritals"],
    queryFn: () => getMaritals(),
  });

  const { data: dataEducation, isLoading: isLoadingEducation } = useQuery({
    queryKey: ["getEducations"],
    queryFn: () => getEducations(),
  });

  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-4 gap-4">
        <FormInput
          placeholder="Agama"
          id="religion_id"
          name="religion_id"
          inputType="react-select"
          sizing="md"
          disabled={isLoadingReligion}
          isLoading={isLoadingReligion}
          options={
            dataReligion?.data.map((item) => ({
              label: item.name,
              value: item.id.toString(),
            })) || []
          }
          isInvalid={!!errors.religion_id}
          errorMessage={errors.religion_id?.message}
        />

        <FormInput
          placeholder="Pekerjaan"
          id="job_id"
          name="job_id"
          inputType="react-select"
          sizing="md"
          disabled={isLoadingJob}
          isLoading={isLoadingJob}
          options={
            dataJob?.data.map((item) => ({
              label: item.name,
              value: item.id.toString(),
            })) || []
          }
          isInvalid={!!errors.job_id}
          errorMessage={errors.job_id?.message}
        />

        <FormInput
          placeholder="Status/Perkawinan"
          id="marital_status_id"
          name="marital_status_id"
          inputType="react-select"
          sizing="md"
          disabled={isLoadingMaritals}
          isLoading={isLoadingMaritals}
          options={
            dataMaritals?.data.map((item) => ({
              label: item.name,
              value: item.id.toString(),
            })) || []
          }
          isInvalid={!!errors.marital_status_id}
          errorMessage={errors.marital_status_id?.message}
        />

        <FormInput
          placeholder="Pendidikan"
          id="education_id"
          name="education_id"
          inputType="react-select"
          sizing="md"
          disabled={isLoadingEducation}
          isLoading={isLoadingEducation}
          options={
            dataEducation?.data.map((item) => ({
              label: item.name,
              value: item.id.toString(),
            })) || []
          }
          isInvalid={!!errors.education_id}
          errorMessage={errors.education_id?.message}
        />
      </div>
      <FormInput
        placeholder="Alamat Kantor"
        id="address_work"
        name="address_work"
        inputType="textarea"
        sizing="md"
        isInvalid={!!errors.address_work}
        errorMessage={errors.address_work?.message}
      />
      <div className="grid grid-cols-2 items-end gap-4">
        <FormInput
          label="Residivis"
          id="residivist"
          name="residivist"
          inputType="radio"
          sizing="md"
          isInvalid={!!errors.residivist}
          errorMessage={errors.residivist?.message}
        />
        <FormInput
          placeholder="Kepartaian"
          id="kepartian"
          name="kepartian"
          inputType="react-select"
          sizing="md"
          options={[
            {
              label: "Ikut Kepartaian",
              value: "Ikut Kepartaian",
            },
            {
              label: "Tidak Ikut Kepartaian",
              value: "Tidak Ikut Kepartaian",
            },
          ]}
          isInvalid={!!errors.kepartian}
          errorMessage={errors.kepartian?.message}
        />
      </div>
    </div>
  );
};
