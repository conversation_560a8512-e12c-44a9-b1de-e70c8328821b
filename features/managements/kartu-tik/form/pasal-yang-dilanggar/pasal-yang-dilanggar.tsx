import { FormInput } from "@/components/ui/form-input";
import React from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";

export const PasalDilanggar = () => {
  const {
    formState: { errors },
  } = useFormContext<FormValues>();

  return (
    <div className="flex">
      <FormInput
        placeholder="Pasal Dilanggar"
        id="pasal_dilanggar"
        name="pasal_dilanggar"
        inputType="textarea"
        sizing="md"
        isInvalid={!!errors.pasal_dilanggar}
        errorMessage={errors.pasal_dilanggar?.message}
      />
    </div>
  );
};
