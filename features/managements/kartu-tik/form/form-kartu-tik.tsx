/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Card } from "flowbite-react";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Accordion } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { DetailPerkara } from "./detail-perkara";
import { InformasiPribadi } from "./informasi-pribadi";
import { InformasiKontak } from "./informasi-kontak";
import { InformasiTambahan } from "./informasi-tambahan";
import { PasalDilanggar } from "./pasal-yang-dilanggar";
import { LatarBelakang } from "./latar-belakang";
import { PutusanPengadilan } from "./putusan-pengadilan";
import { InformasiTambahanLanjutan } from "./informasi-tambahan-lanjutan";
import { <PERSON>tenti<PERSON><PERSON> } from "./otentikasi";
import { formSchema, FormValues } from "./schema";
import { FormInput } from "@/components/ui/form-input";
import { usePopupStore } from "@/stores/popup/popup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createSuspect } from "@/services/form/form-post";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  HiOutlineCheckCircle,
  HiOutlineExclamationCircle,
} from "react-icons/hi";
import { DataCreateForm, ErrorResponse } from "./form-kartu-tik-interface";
import { useLoadingOverlayStore } from "@/stores/loading-overlay/loading-overlay";
import dayjs from "dayjs";

export const FormKartuTik = () => {
  const navigation = useRouter();
  const queryClient = useQueryClient();
  const { data: dataSession } = useSession();
  const { openPopup, closePopup } = usePopupStore();
  const { show, hide } = useLoadingOverlayStore();
  const role = dataSession?.user?.role;
  const unitName = dataSession?.user?.kejaksaan_unit?.name;

  const FORMS = [
    {
      title: "Detail Perkara",
      content: <DetailPerkara />,
    },
    {
      title: "Informasi Pribadi",
      content: <InformasiPribadi />,
    },
    {
      title: "Informasi Kontak",
      content: <InformasiKontak />,
    },
    {
      title: "Informasi Tambahan",
      content: <InformasiTambahan />,
    },
    {
      title: "Pasal yang Dilanggar",
      content: <PasalDilanggar />,
    },
    {
      title: "Latar Belakang",
      content: <LatarBelakang />,
    },
    {
      title: "Putusan Pengadilan",
      content: <PutusanPengadilan />,
    },
    {
      title: "Informasi Tambahan",
      content: <InformasiTambahanLanjutan />,
    },
    {
      title: "Otentikasi",
      content: <Otentikasi />,
    },
  ];

  const submitSuspect = useMutation({
    mutationFn: (data: DataCreateForm) => createSuspect(data),
    onSuccess: () => {
      hide();
      queryClient.invalidateQueries({ queryKey: ["listForm"] });
      openPopup({
        title: "Berhasil Menyimpan Data",
        message: "Data berhasil disimpan",
        classNameContainerActions: "justify-end",
        icon: (
          <div>
            <div className="flex h-12 w-12 items-center justify-center rounded-full border-8 border-green-100 bg-green-200">
              <HiOutlineCheckCircle className="h-6 w-6 text-green-700" />
            </div>
          </div>
        ),
        withHeader: false,
        size: "xl",
        actions: [
          {
            label: "Tutup",
            type: "button",
            onClick: () => {
              closePopup();
              navigation.replace(`/${role}/managements/form`);
            },
          },
        ],
      });
    },
    onError: (error: ErrorResponse) => {
      hide();
      openPopup({
        title: "Gagal Menyimpan Data",
        message: `Terjadi kesalahan saat menyimpan data, silahkan coba lagi. ${error?.response?.data?.message}`,
        classNameContainerActions: "justify-end",
        icon: (
          <div>
            <div className="flex h-12 w-12 items-center justify-center rounded-full border-8 border-red-100 bg-red-200">
              <HiOutlineExclamationCircle className="h-6 w-6 text-red-500" />
            </div>
          </div>
        ),
        size: "xl",
        actions: [
          {
            label: "Kembali",
            type: "button",
            onClick: () => closePopup(),
          },
        ],
      });
    },
  });

  const handleCreateForm = (payload: DataCreateForm) => {
    show("Menyimpan data...");
    submitSuspect.mutateAsync(payload);
  };

  const formManagement = useForm<FormValues>({
    mode: "onChange",
    resolver: zodResolver(formSchema),
    defaultValues: {
      ref_number: "",
      letter_code: "",
      suspect_type: "",
      suspect_origin: "",
      suspect_status: "",
      full_name: "",
      call_name: "",
      birth_place: "",
      birth_date: dayjs().subtract(10, "year").format("DD/MM/YYYY"),
      gender: "",
      kewarganegaraan: "",
      ethnic: "",
      province_id: "",
      city_id: "",
      subdistrict_id: "",
      ward_id: "",
      postal_code: "",
      address: "",
      phone_number: [{ value: "" }],
      type_idendity: "",
      identity_number: "",
      education_id: "",
      job_id: "",
      marital_status_id: "",
      address_work: "",
      residivist: "",
      kepartian: "",
      religion_id: "",
      pasal_dilanggar: "",
      latar_belakang: "",
      putusan_pengadilan_negri: "",
      putusan_pengadilan_tinggi: "",
      putusan_mahkama_agung: "",
      nama_orangtua: "",
      nama_kawanan: "",
      alamat_orangtua: "",
      informasi_lainnya: "",
      spk3_skpp_tempat_lahir: "",
      spk3_skpp_tanggal_lahir: "",
    },
  });

  const {
    handleSubmit,
    formState: { errors },
  } = formManagement;

  const onSubmit = async (data: FormValues) => {
    const ids = [
      "religion_id",
      "residivist",
      "marital_status_id",
      "job_id",
      "education_id",
      "ward_id",
      "subdistrict_id",
      "city_id",
      "province_id",
    ];
    const formattedData: any = { ...data };
    ids.forEach((id) => {
      formattedData[id as keyof FormValues] = Number(
        data[id as keyof FormValues],
      );
    });
    formattedData.phone_number = formattedData.phone_number.map(
      (item: { value: string }) => item.value,
    );

    openPopup({
      title: "Konfirmasi Simpan Data",
      message: "Apakah kamu yakin ingin menyimpan data ini?",
      classNameContainerActions: "justify-end",
      withHeader: false,
      size: "xl",
      actions: [
        {
          label: "Kembali",
          type: "button",
          variant: "outline",
          onClick: () => closePopup(),
        },
        {
          label: "Simpan Data",
          type: "submit",
          onClick: () => handleCreateForm(formattedData),
        },
      ],
    });
  };

  return (
    <FormProvider {...formManagement}>
      <Card className="border-main-2 space-y-4 shadow-none">
        <div className="flex justify-between">
          <h5 className="font-semibold">{unitName}</h5>
          <FormInput
            placeholder="D.IN.15"
            className="tik-number"
            id="ref_number"
            name="ref_number"
            inputType="text"
            sizing="md"
            isInvalid={!!errors.ref_number}
            errorMessage={errors.ref_number?.message}
          />
        </div>
        <div className="flex flex-col items-center">
          <h6 className="text-sm font-semibold underline">
            KARTU TIK/TERSANGKA/TERDAKWA/TERPIDANA/DPO
          </h6>
          <div className="flex items-center gap-1">
            <p className="text-sm">Nomor : R -</p>
            <FormInput
              placeholder="Nomor Surat"
              className="ref-number"
              id="letter_code"
              name="letter_code"
              inputType="text"
              sizing="md"
              isInvalid={!!errors.letter_code}
              errorMessage={errors.letter_code?.message}
            />
          </div>
        </div>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Accordion items={FORMS} />
          <div className="mt-4 flex justify-end gap-4">
            <Button size="sm" variant="outline">
              Kembali
            </Button>
            <Button size="sm" type="submit">
              Simpan Data
            </Button>
          </div>
        </form>
      </Card>
    </FormProvider>
  );
};
