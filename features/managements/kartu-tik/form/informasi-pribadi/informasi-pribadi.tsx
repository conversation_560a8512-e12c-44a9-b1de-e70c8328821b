import { FormInput } from "@/components/ui/form-input";
import React from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";
import { DROPDOWN_GENDER, DROPDOWN_KEWARGANEGARAAN } from "@/constants";
import { useQuery } from "@tanstack/react-query";
import {
  getDistricts,
  getProvinces,
  getRegencies,
  getVillages,
} from "@/services/master/master-get";

export const InformasiPribadi = () => {
  const {
    watch,
    formState: { errors },
  } = useFormContext<FormValues>();

  const kewarganegaraan = watch("kewarganegaraan");
  const provinceId = watch("province_id");
  const cityId = watch("city_id");
  const districtId = watch("subdistrict_id");

  const { data: dataProvinces, isLoading: isLoadingProvinces } = useQuery({
    queryKey: ["getProvinces"],
    queryFn: () => getProvinces(),
    enabled: kewarganegaraan === "indonesian",
  });

  const { data: dataRegencies, isLoading: isLoadingRegencies } = useQuery({
    queryKey: ["getRegencies", provinceId],
    queryFn: () => getRegencies(provinceId as string),
    enabled: kewarganegaraan === "indonesian" && !!provinceId,
  });

  const { data: dataDistricts, isLoading: isLoadingDistricts } = useQuery({
    queryKey: ["getDistricts", provinceId, cityId],
    queryFn: () => getDistricts(cityId as string),
    enabled: kewarganegaraan === "indonesian" && !!provinceId && !!cityId,
  });

  const { data: dataVillages, isLoading: isLoadingVillages } = useQuery({
    queryKey: ["getVillages", provinceId, cityId, districtId],
    queryFn: () => getVillages(districtId as string),
    enabled:
      kewarganegaraan === "indonesian" &&
      !!provinceId &&
      !!cityId &&
      !!districtId,
  });

  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-1 items-end gap-4 md:grid-cols-3">
        <FormInput
          placeholder="Nama Lengkap"
          id="full_name"
          name="full_name"
          inputType="text"
          sizing="md"
          maxCharacters={100}
          isInvalid={!!errors.full_name}
          errorMessage={errors.full_name?.message}
        />
        <FormInput
          placeholder="Nama Samaran/Panggilan"
          id="call_name"
          name="call_name"
          inputType="text"
          sizing="md"
          maxCharacters={100}
          isInvalid={!!errors.call_name}
          errorMessage={errors.call_name?.message}
        />
        <FormInput
          placeholder="Jenis Kelamin"
          id="gender"
          name="gender"
          inputType="react-select"
          sizing="md"
          options={DROPDOWN_GENDER}
          isInvalid={!!errors.gender}
          errorMessage={errors.gender?.message}
        />
      </div>
      <div className="grid grid-cols-4 items-end gap-4">
        <FormInput
          placeholder="Tempat Lahir"
          id="birth_place"
          name="birth_place"
          inputType="text"
          maxCharacters={100}
          isInvalid={!!errors.birth_place}
          errorMessage={errors.birth_place?.message}
        />
        <FormInput
          placeholder="Tanggal Lahir"
          id="birth_date"
          name="birth_date"
          inputType="date"
          dateFormat="DD/MM/YYYY"
          maxDate={new Date()}
          isInvalid={!!errors.birth_date}
          errorMessage={errors.birth_date?.message}
        />
        <FormInput
          placeholder="Pilih Kewarganegaraan"
          id="kewarganegaraan"
          name="kewarganegaraan"
          inputType="react-select"
          sizing="md"
          options={DROPDOWN_KEWARGANEGARAAN}
          isInvalid={!!errors.kewarganegaraan}
          errorMessage={errors.kewarganegaraan?.message}
          reactSelectProps={{
            isClearable: true,
          }}
        />
        <FormInput
          placeholder="Bangsa/Suku"
          id="ethnic"
          name="ethnic"
          inputType="text"
          sizing="md"
          maxCharacters={100}
          isInvalid={!!errors.ethnic}
          errorMessage={errors.ethnic?.message}
        />
      </div>
      {kewarganegaraan === "indonesian" && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          <FormInput
            placeholder="Provinsi"
            id="province_id"
            name="province_id"
            inputType="react-select"
            isLoading={isLoadingProvinces}
            disabled={isLoadingProvinces}
            sizing="md"
            options={
              dataProvinces?.data?.map((province) => ({
                value: province.id.toString(),
                label: province.name,
              })) || []
            }
            isInvalid={!!errors.province_id}
            errorMessage={errors.province_id?.message}
          />
          <FormInput
            placeholder="Kota / Kabupaten"
            id="city_id"
            name="city_id"
            inputType="react-select"
            sizing="md"
            disabled={isLoadingProvinces || isLoadingRegencies}
            options={
              dataRegencies?.data?.map((regency) => ({
                value: regency.id.toString(),
                label: regency.name,
              })) || []
            }
            isInvalid={!!errors.city_id}
            errorMessage={errors.city_id?.message}
          />
          <FormInput
            placeholder="Kecamatan"
            id="subdistrict_id"
            name="subdistrict_id"
            inputType="react-select"
            sizing="md"
            disabled={
              isLoadingProvinces || isLoadingRegencies || isLoadingDistricts
            }
            options={
              dataDistricts?.data?.map((district) => ({
                value: district.id.toString(),
                label: district.name,
              })) || []
            }
            isInvalid={!!errors.subdistrict_id}
            errorMessage={errors.subdistrict_id?.message}
          />
          <FormInput
            placeholder="Kelurahan"
            id="ward_id"
            name="ward_id"
            inputType="react-select"
            sizing="md"
            disabled={
              isLoadingProvinces ||
              isLoadingRegencies ||
              isLoadingDistricts ||
              isLoadingVillages
            }
            options={
              dataVillages?.data?.map((village) => ({
                value: village.id.toString(),
                label: village.name,
              })) || []
            }
            isInvalid={!!errors.ward_id}
            errorMessage={errors.ward_id?.message}
          />
          <FormInput
            placeholder="Kodepos"
            id="postal_code"
            name="postal_code"
            inputType="text"
            sizing="md"
            isInvalid={!!errors.postal_code}
            errorMessage={errors.postal_code?.message}
          />
        </div>
      )}
      <FormInput
        placeholder="Alamat / Tempat Tinggal"
        id="address"
        name="address"
        inputType="textarea"
        sizing="md"
        isInvalid={!!errors.address}
        errorMessage={errors.address?.message}
      />
    </div>
  );
};
