import { Button } from "@/components/ui/button";
import { FormInput } from "@/components/ui/form-input";
import React from "react";
import { HiPlus, HiTrash } from "react-icons/hi";
import { useFieldArray, useFormContext } from "react-hook-form";
import { FormValues } from "../schema";
import { DROPDOWN_JENIS_IDENTITAS } from "@/constants";

export const InformasiKontak = () => {
  const {
    control,
    formState: { errors },
  } = useFormContext<FormValues>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "phone_number",
  });

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium text-gray-900 dark:text-white">
          Phone Number
        </label>
        {fields.map((field, index) => (
          <div key={field.id} className="grid grid-cols-5 items-center gap-4">
            <FormInput
              placeholder="8123123"
              id={`phone_number.${index}.value`}
              name={`phone_number.${index}.value`}
              inputType="phone"
              sizing="md"
              isInvalid={!!errors.phone_number?.[index]?.value}
              errorMessage={errors.phone_number?.[index]?.value?.message}
              className="col-span-4"
            />
            {index !== 0 && (
              <Button
                size="sm"
                leftIcon={<HiTrash />}
                className="mb-1.5 h-[45px] border-red-500 text-red-500 hover:bg-red-50"
                onClick={() => remove(index)}
                variant="outline"
              >
                <span className="hidden md:block">Hapus</span>
              </Button>
            )}
            {index === 0 && (
              <Button
                size="sm"
                className="mb-1.5 h-[45px]"
                leftIcon={<HiPlus />}
                onClick={() => {
                  append({ value: "" });
                }}
              >
                <span>Tambah Nomor HP</span>
              </Button>
            )}
          </div>
        ))}
        {errors.phone_number?.message && (
          <p className="text-sm text-red-600">{errors.phone_number.message}</p>
        )}
      </div>
      <div className="grid grid-cols-2 gap-4">
        <FormInput
          placeholder="Jenis Identitas"
          id="type_idendity"
          name="type_idendity"
          inputType="react-select"
          sizing="md"
          options={DROPDOWN_JENIS_IDENTITAS}
          isInvalid={!!errors.type_idendity}
          errorMessage={errors.type_idendity?.message}
        />
        <FormInput
          placeholder="Nomor Identitas"
          id="identity_number"
          name="identity_number"
          inputType="text"
          sizing="md"
          isInvalid={!!errors.identity_number}
          errorMessage={errors.identity_number?.message}
        />
      </div>
    </div>
  );
};
