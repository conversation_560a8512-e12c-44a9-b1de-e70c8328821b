import { FormInput } from "@/components/ui/form-input";
import { Card } from "flowbite-react";
import React from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";

export const Otentikasi = () => {
  const {
    // formState: { errors },
  } = useFormContext<FormValues>();
  // const detailErrors = errors.otentikasi || {};

  return (
    <div className="flex flex-col gap-2">
      <div className="grid grid-cols-2 gap-4">
        <FormInput
          placeholder="Pas Photo 4x6"
          label="Pas Photo 4x6"
          id="otentikasi.pasFoto"
          name="otentikasi.pasFoto"
          className="w-full"
          inputType="dropzone"
          sizing="md"
          // isInvalid={!!detailErrors.pasFoto}
          // errorMessage={detailErrors.pasFoto?.message}
        />
        <FormInput
          placeholder="Tanda Tangan"
          label="Tanda Tangan"
          id="otentikasi.tandaTangan"
          name="otentikasi.tandaTangan"
          inputType="signature"
          sizing="md"
          // isInvalid={!!detailErrors.tandaTangan}
          // errorMessage={detailErrors.tandaTangan?.message}
        />
      </div>
      <div className="space-y-8 pt-6">
        <Card className="border-main-2 shadow-none">
          <h5 className="font-sm text-main-1 font-bold">
            Sidik Jari Tangan Kiri
          </h5>
          <div className="grid grid-cols-5 gap-4">
            <FormInput
              placeholder="Ibu Jari"
              label="Ibu Jari"
              id="otentikasi.tanganKiriIbuJari"
              name="otentikasi.tanganKiriIbuJari"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKiriIbuJari}
              // errorMessage={detailErrors.tanganKiriIbuJari?.message}
            />
            <FormInput
              placeholder="Telunjuk"
              label="Telunjuk"
              id="otentikasi.tanganKiriTelunjuk"
              name="otentikasi.tanganKiriTelunjuk"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKiriTelunjuk}
              // errorMessage={detailErrors.tanganKiriTelunjuk?.message}
            />
            <FormInput
              placeholder="Tengah"
              label="Tengah"
              id="otentikasi.tanganKiriTengah"
              name="otentikasi.tanganKiriTengah"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKiriTengah}
              // errorMessage={detailErrors.tanganKiriTengah?.message}
            />
            <FormInput
              placeholder="Manis"
              label="Manis"
              id="otentikasi.tanganKiriManis"
              name="otentikasi.tanganKiriManis"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKiriManis}
              // errorMessage={detailErrors.tanganKiriManis?.message}
            />
            <FormInput
              placeholder="Kelingking"
              label="Kelingking"
              id="otentikasi.tanganKiriKelingking"
              name="otentikasi.tanganKiriKelingking"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKiriKelingking}
              // errorMessage={detailErrors.tanganKiriKelingking?.message}
            />
          </div>
        </Card>
        <Card className="border-main-2 shadow-none">
          <h5 className="font-sm text-main-1 font-bold">
            Sidik Jari Tangan Kanan
          </h5>
          <div className="grid grid-cols-5 gap-4">
            <FormInput
              placeholder="Ibu Jari"
              label="Ibu Jari"
              id="otentikasi.tanganKananIbuJari"
              name="otentikasi.tanganKananIbuJari"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKananIbuJari}
              // errorMessage={detailErrors.tanganKananIbuJari?.message}
            />
            <FormInput
              placeholder="Telunjuk"
              label="Telunjuk"
              id="otentikasi.tanganKananTelunjuk"
              name="otentikasi.tanganKananTelunjuk"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKananTelunjuk}
              // errorMessage={detailErrors.tanganKananTelunjuk?.message}
            />
            <FormInput
              placeholder="Tengah"
              label="Tengah"
              id="otentikasi.tanganKananTengah"
              name="otentikasi.tanganKananTengah"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKananTengah}
              // errorMessage={detailErrors.tanganKananTengah?.message}
            />
            <FormInput
              placeholder="Manis"
              label="Manis"
              id="otentikasi.tanganKananManis"
              name="otentikasi.tanganKananManis"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKananManis}
              // errorMessage={detailErrors.tanganKananManis?.message}
            />
            <FormInput
              placeholder="Kelingking"
              label="Kelingking"
              id="otentikasi.tanganKananKelingking"
              name="otentikasi.tanganKananKelingking"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.tanganKananKelingking}
              // errorMessage={detailErrors.tanganKananKelingking?.message}
            />
          </div>
        </Card>
        <Card className="border-main-2 shadow-none">
          <h5 className="font-sm text-main-1 font-bold">Telapak Tangan</h5>
          <div className="grid grid-cols-2 gap-4">
            <FormInput
              placeholder="Telapak Tangan Kiri"
              label="Telapak Tangan Kiri"
              id="otentikasi.telapakTanganKiri"
              name="otentikasi.telapakTanganKiri"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.telapakTanganKiri}
              // errorMessage={detailErrors.telapakTanganKiri?.message}
            />
            <FormInput
              placeholder="Telapak Tangan Kanan"
              label="Telapak Tangan Kanan"
              id="otentikasi.telapakTanganKanan"
              name="otentikasi.telapakTanganKanan"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.telapakTanganKanan}
              // errorMessage={detailErrors.telapakTanganKanan?.message}
            />
          </div>
        </Card>
        <Card className="border-main-2 shadow-none">
          <h5 className="font-sm text-main-1 font-bold">Iris Mata</h5>
          <div className="grid grid-cols-2 gap-4">
            <FormInput
              placeholder="Mata Kiri"
              label="Mata Kiri"
              id="otentikasi.irisMataKiri"
              name="otentikasi.irisMataKiri"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.irisMataKiri}
              // errorMessage={detailErrors.irisMataKiri?.message}
            />
            <FormInput
              placeholder="Mata Kanan"
              label="Mata Kanan"
              id="otentikasi.irisMataKanan"
              name="otentikasi.irisMataKanan"
              inputType="dropzone"
              sizing="md"
              // isInvalid={!!detailErrors.irisMataKanan}
              // errorMessage={detailErrors.irisMataKanan?.message}
            />
          </div>
        </Card>
      </div>
    </div>
  );
};
