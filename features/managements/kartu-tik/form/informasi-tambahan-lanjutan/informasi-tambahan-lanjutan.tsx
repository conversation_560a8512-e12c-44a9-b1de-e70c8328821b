import { FormInput } from "@/components/ui/form-input";
import React from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";

export const InformasiTambahanLanjutan = () => {
  const {
    formState: { errors },
  } = useFormContext<FormValues>();

  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-2 gap-4">
        <FormInput
          placeholder="Nama Orangtua"
          id="nama_orangtua"
          name="nama_orangtua"
          inputType="text"
          sizing="md"
          maxCharacters={100}
          isInvalid={!!errors.nama_orangtua}
          errorMessage={errors.nama_orangtua?.message}
        />
        <FormInput
          placeholder="Nama Kawan"
          id="nama_kawanan"
          name="nama_kawanan"
          inputType="text"
          sizing="md"
          maxCharacters={100}
          isInvalid={!!errors.nama_kawanan}
          errorMessage={errors.nama_kawanan?.message}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <FormInput
          placeholder="Alamat Orangtua"
          id="alamat_orangtua"
          name="alamat_orangtua"
          inputType="textarea"
          sizing="md"
          isInvalid={!!errors.alamat_orangtua}
          errorMessage={errors.alamat_orangtua?.message}
        />
        <FormInput
          placeholder="Informasi Lainnya"
          id="informasi_lainnya"
          name="informasi_lainnya"
          inputType="textarea"
          sizing="md"
          isInvalid={!!errors.informasi_lainnya}
          errorMessage={errors.informasi_lainnya?.message}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <FormInput
          label="SPK3/SKPP"
          placeholder="SPK3/SKPP"
          id="spk3_skpp_tempat_lahir"
          name="spk3_skpp_tempat_lahir"
          inputType="text"
          sizing="md"
          isInvalid={!!errors.spk3_skpp_tempat_lahir}
          errorMessage={errors.spk3_skpp_tempat_lahir?.message}
        />
        <FormInput
          label="Tanggal SPK3/SKPP"
          placeholder="Tanggal SPK3/SKPP"
          id="spk3_skpp_tanggal_lahir"
          name="spk3_skpp_tanggal_lahir"
          inputType="date"
          sizing="md"
          isInvalid={!!errors.spk3_skpp_tanggal_lahir}
          errorMessage={errors.spk3_skpp_tanggal_lahir?.message}
        />
      </div>
    </div>
  );
};
