import { z } from "zod";

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const otentikasiSchema = z.object({
  pasFoto: z.string({
    required_error: "Pas photo harus diisi",
  }),
  tandaTangan: z.string({
    required_error: "Tanda Tangan harus diisi",
  }),
  tanganKiriIbuJari: z.string({
    required_error: "Sidik jari tangan kiri ibu jari harus diisi",
  }),
  tanganKiriTelunjuk: z.string({
    required_error: "Sidik jari tangan kiri telunjuk harus diisi",
  }),
  tanganKiriTengah: z.string({
    required_error: "Sidik jari tangan kiri tengah harus diisi",
  }),
  tanganKiriManis: z.string({
    required_error: "Sidik jari tangan kiri manis harus diisi",
  }),
  tanganKiriKelingking: z.string({
    required_error: "Sidik jari tangan kiri kelingking harus diisi",
  }),
  tanganKananIbuJari: z.string({
    required_error: "Sidik jari tangan kanan ibu jari harus diisi",
  }),
  tanganKananTelunjuk: z.string({
    required_error: "Sidik jari tangan kanan telunjuk harus diisi",
  }),
  tanganKananTengah: z.string({
    required_error: "Sidik jari tangan kanan tengah harus diisi",
  }),
  tanganKananManis: z.string({
    required_error: "Sidik jari tangan kanan manis harus diisi",
  }),
  tanganKananKelingking: z.string({
    required_error: "Sidik jari tangan kanan kelingking harus diisi",
  }),
  telapakTanganKanan: z.string({
    required_error: "Telapak tangan kanan kelingking harus diisi",
  }),
  telapakTanganKiri: z.string({
    required_error: "Telapak tangan kiri kelingking harus diisi",
  }),
  irisMataKiri: z.string({
    required_error: "Iris mata kiri kelingking harus diisi",
  }),
  irisMataKanan: z.string({
    required_error: "Iris mata kanan kelingking harus diisi",
  }),
});

export const formSchema = z.object({
  // card_tik: z.string({
  //   required_error: "Nomor Kartu TIK harus diisi",
  // }),
  ref_number: z
    .string({
      required_error: "Nomor Referensi harus diisi",
    })
    .nonempty("Nomor Referensi harus diisi"),
  letter_code: z
    .string({
      required_error: "Nomor Surat harus diisi",
    })
    .nonempty("Nomor Surat harus diisi"),
  // Detail Perkara
  suspect_type: z
    .string({
      required_error: "Jenis Pidana harus dipilih",
    })
    .nonempty("Jenis Pidana harus dipilih"),
  suspect_origin: z
    .string({
      required_error: "Asal Perkara harus dipilih",
    })
    .nonempty("Asal Perkara harus dipilih"),
  suspect_status: z
    .string({
      required_error: "Status Perkara harus dipilih",
    })
    .nonempty("Status Perkara harus dipilih"),
  // Informasi Pribadi
  full_name: z
    .string({
      required_error: "Nama Lengkap harus diisi",
    })
    .nonempty("Nama Lengkap harus diisi")
    .max(100, { message: "Nama Lengkap maksimal 100 karakter" }),
  call_name: z
    .string({
      required_error: "Nama Samaran/Panggilan harus diisi",
    })
    .nonempty("Nama Samaran/Panggilan harus diisi")
    .max(100, { message: "Nama Samaran/Panggilan maksimal 100 karakter" }),
  birth_place: z
    .string({
      required_error: "Tempat Lahir harus diisi",
    })
    .nonempty("Tempat Lahir harus diisi"),
  birth_date: z
    .string({
      required_error: "Tanggal Lahir harus diisi",
    })
    .nonempty("Tanggal Lahir harus diisi"),
  gender: z
    .string({
      required_error: "Jenis Kelamin harus dipilih",
    })
    .nonempty("Jenis Kelamin harus dipilih"),
  kewarganegaraan: z
    .string({
      required_error: "Kewarganegaraan harus dipilih",
    })
    .nonempty("Kewarganegaraan harus dipilih"),
  ethnic: z
    .string({
      required_error: "Bangsa/Suku harus diisi",
    })
    .nonempty("Bangsa/Suku harus diisi")
    .max(100, { message: "Bangsa/Suku maksimal 100 karakter" }),
  province_id: z.string().optional(),
  city_id: z.string().optional(),
  subdistrict_id: z.string().optional(),
  ward_id: z.string().optional(),
  postal_code: z
    .string()
    .max(100, { message: "Kodepos maksimal 100 karakter" })
    .optional(),
  address: z
    .string({
      required_error: "Alamat harus diisi",
    })
    .nonempty("Alamat harus diisi")
    .max(255, { message: "Alamat maksimal 255 karakter" }),
  // Informasi Kontak
  phone_number: z
    .array(
      z.object({
        value: z
          .string({
            required_error: "Nomor HP harus diisi",
          })
          .nonempty("Nomor HP harus diisi")
          .max(16, { message: "Nomor HP maksimal 16 karakter" })
          .refine((value) => /^[0-9]+$/.test(value?.split("+")[1]), {
            message: "Nomor HP hanya boleh berisi angka 0-9",
          }),
      }),
    )
    .min(1, { message: "Minimal satu nomor HP harus diisi" }),
  type_idendity: z
    .string({
      required_error: "Jenis Identitas harus diisi",
    })
    .nonempty("Jenis Identitas harus diisi"),
  identity_number: z
    .string({
      required_error: "Nomor Identitas harus diisi",
    })
    .nonempty("Nomor Identitas harus diisi")
    .max(100, { message: "Nomor Identitas maksimal 100 karakter" })
    .refine((value) => /^[0-9]+$/.test(value), {
      message: "Nomor Identitas hanya boleh berisi angka 0-9",
    }),
  // Informasi Tambahan
  education_id: z
    .string({
      required_error: "Pendidikan harus dipilih",
    })
    .nonempty("Pendidikan harus dipilih"),
  job_id: z
    .string({
      required_error: "Pekerjaan harus dipilih",
    })
    .nonempty("Pekerjaan harus dipilih"),
  marital_status_id: z
    .string({
      required_error: "Status Perkawinan harus dipilih",
    })
    .nonempty("Status Perkawinan harus dipilih"),
  address_work: z
    .string({
      required_error: "Alamat Kantor harus diisi",
    })
    .nonempty("Alamat Kantor harus diisi"),
  residivist: z
    .string({
      required_error: "Residivist harus dipilih",
    })
    .nonempty("Residivist harus dipilih"),
  kepartian: z
    .string({
      required_error: "Kepartaian harus diisi",
    })
    .nonempty("Kepartaian harus diisi"),
  religion_id: z
    .string({
      required_error: "Agama harus dipilih",
    })
    .nonempty("Agama harus dipilih"),
  // Pasal Dilanggar
  pasal_dilanggar: z
    .string({
      required_error: "Pasal Dilanggar harus diisi",
    })
    .nonempty("Pasal Dilanggar harus diisi"),
  // Latar Belakang
  latar_belakang: z
    .string({
      required_error: "Latar Belakang harus diisi",
    })
    .nonempty("Latar Belakang harus diisi"),
  // Putusan Pengadilan
  putusan_pengadilan_negri: z
    .string({
      required_error: "Putusan Pengadilan Negeri harus diisi",
    })
    .nonempty("Putusan Pengadilan Negeri harus diisi"),
  putusan_pengadilan_tinggi: z
    .string({
      required_error: "Putusan Pengadilan Tinggi harus diisi",
    })
    .nonempty("Putusan Pengadilan Tinggi harus diisi"),
  putusan_mahkama_agung: z
    .string({
      required_error: "Putusan Mahkamah Agung harus diisi",
    })
    .nonempty("Putusan Mahkamah Agung harus diisi"),
  // Informasi Tambahan Lanjutan
  nama_orangtua: z
    .string({
      required_error: "Nama Orangtua harus diisi",
    })
    .nonempty("Nama Orangtua harus diisi")
    .max(100, { message: "Nama Orangtua maksimal 100 karakter" }),
  nama_kawanan: z
    .string({
      required_error: "Nama Kawan harus diisi",
    })
    .nonempty("Nama Kawan harus diisi")
    .max(100, { message: "Nama Kawan maksimal 100 karakter" }),
  alamat_orangtua: z
    .string({
      required_error: "Alamat Orangtua harus diisi",
    })
    .nonempty("Alamat Orangtua harus diisi")
    .max(255, { message: "Alamat Orangtua maksimal 255 karakter" }),
  informasi_lainnya: z.string().optional(),
  spk3_skpp_tempat_lahir: z
    .string({
      required_error: "SPK3/SKPP Tempat Lahir harus diisi",
    })
    .nonempty("SPK3/SKPP Tempat Lahir harus diisi")
    .max(255, { message: "SPK3/SKPP Tempat Lahir maksimal 255 karakter" }),
  spk3_skpp_tanggal_lahir: z
    .string({
      required_error: "SPK3/SKPP Tanggal Lahir harus diisi",
    })
    .nonempty("SPK3/SKPP Tanggal Lahir harus diisi"),
  // Otentikasi
  // otentikasi: otentikasiSchema,
});

export type FormValues = z.infer<typeof formSchema>;
