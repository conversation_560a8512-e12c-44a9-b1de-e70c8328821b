import { FormInput } from "@/components/ui/form-input";
import React from "react";
import { useFormContext } from "react-hook-form";
import { FormValues } from "../schema";

export const PutusanPengadilan = () => {
  const {
    formState: { errors },
  } = useFormContext<FormValues>();

  return (
    <div className="flex flex-col gap-4">
      <FormInput
        placeholder="Masukkan Putusan Pengadilan Negeri"
        label="Putusan Pengadilan Negeri"
        id="putusan_pengadilan_negri"
        name="putusan_pengadilan_negri"
        inputType="textarea"
        sizing="md"
        isInvalid={!!errors.putusan_pengadilan_negri}
        errorMessage={errors.putusan_pengadilan_negri?.message}
      />
      <FormInput
        placeholder="Masukkan Putusan Pengadilan Tinggi"
        label="Putusan Pengadilan Tinggi"
        id="putusan_pengadilan_tinggi"
        name="putusan_pengadilan_tinggi"
        inputType="textarea"
        sizing="md"
        isInvalid={!!errors.putusan_pengadilan_tinggi}
        errorMessage={errors.putusan_pengadilan_tinggi?.message}
      />
      <FormInput
        placeholder="Masukkan Putusan Mahkamah Agung"
        label="Putusan Mahkamah Agung"
        id="putusan_mahkama_agung"
        name="putusan_mahkama_agung"
        inputType="textarea"
        sizing="md"
        isInvalid={!!errors.putusan_mahkama_agung}
        errorMessage={errors.putusan_mahkama_agung?.message}
      />
    </div>
  );
};
