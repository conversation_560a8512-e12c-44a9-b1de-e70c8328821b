"use client";
import { PageHeader } from "@/components/ui/page-header/page-header";
import { routes } from "@/lib/routes";
import { UserData } from "@/types/auth";
import React from "react";
import {
  HiOutlineArrowLeft,
  HiOutlineDownload,
  HiOutlinePrinter,
  HiPencil,
} from "react-icons/hi";
import { CardDetailTik } from "./card-detail-tik";

export interface DetailKartuTikProps {
  userData?: UserData;
}

export const DetailKartuTik = ({ userData }: DetailKartuTikProps) => {
  const role = userData?.role;
  const unitName = userData?.kejaksaan_unit?.name;

  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        className="border-b border-gray-300 pb-6"
        title={`Kartu TIK [${userData?.full_name}]`}
        backHref={routes.managements.form.index}
        backIcon={<HiOutlineArrowLeft className="h-5 w-5" />}
        rightElement={[
          {
            icon: <HiOutlinePrinter className="h-5 w-5" />,
            label: "Cetak Dokumen",
            isButton: true,
            onClick: () => console.log("Cetak"),
          },
          {
            icon: <HiOutlineDownload className="h-5 w-5" />,
            label: "Download",
            variant: "outline",
            isButton: true,
            onClick: () => console.log("Download"),
          },
          {
            icon: <HiPencil className="h-5 w-5" />,
            label: "Edit Data",
            variant: "outline",
            isButton: true,
            onClick: () => console.log("Edit"),
          },
        ]}
      />
      <CardDetailTik unitName={unitName} role={role} />
    </div>
  );
};
