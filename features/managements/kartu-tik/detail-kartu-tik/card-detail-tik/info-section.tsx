import { InfoRow } from "./info-row";

interface InfoSectionProps {
  title: string;
  data: { label: string; value?: string | number | null }[];
}

export const InfoSection = ({ title, data }: InfoSectionProps) => {
  return (
    <section className="space-y-4">
      <h5 className="font-semibold underline decoration-2 underline-offset-4">{title}</h5>
      <div className="grid grid-cols-1 gap-y-2 text-sm md:grid-cols-2 md:gap-x-8">
        {data.map((item, index) => (
          <InfoRow key={index} label={item.label} value={item.value} />
        ))}
      </div>
    </section>
  );
};
