import { useState } from "react";
import { ModalPreviewImage } from "@/components/ui/modal/modal-preview-image";

interface BiometricData {
  left_thumb?: string;
  left_index?: string;
  left_middle?: string;
  left_ring?: string;
  left_pinky?: string;
  right_thumb?: string;
  right_index?: string;
  right_middle?: string;
  right_ring?: string;
  right_pinky?: string;
  left_palm?: string;
  right_palm?: string;
  left_iris?: string;
  right_iris?: string;
}

interface BiometricSectionProps {
  title: string;
  biometricData?: BiometricData;
  signature?: string;
}

interface FingerPrintItemProps {
  label: string;
  imageUrl?: string;
}

const FingerPrintItem = ({ label, imageUrl }: FingerPrintItemProps) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <div className="flex flex-col items-center space-y-2">
        <div 
          className="flex h-16 w-16 cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 transition-colors hover:bg-gray-100"
          onClick={() => imageUrl && setShowModal(true)}
        >
          {imageUrl ? (
            <img 
              src={imageUrl} 
              alt={label}
              className="h-full w-full rounded-lg object-cover"
            />
          ) : (
            <div className="text-gray-400">
              <svg className="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
        <span className="text-xs text-center text-gray-600">{label}</span>
      </div>
      
      <ModalPreviewImage
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        imageUrl={imageUrl || null}
        title={label}
        alt={label}
      />
    </>
  );
};

export const BiometricSection = ({ title, biometricData, signature }: BiometricSectionProps) => {
  return (
    <section className="space-y-6">
      <h5 className="font-semibold underline decoration-2 underline-offset-4">{title}</h5>
      
      {/* Signature */}
      <div className="flex justify-center">
        <div className="text-center">
          <div className="mb-2 flex h-20 w-32 items-center justify-center border-2 border-gray-300 bg-gray-50">
            {signature ? (
              <img src={signature} alt="Tanda Tangan" className="h-full w-full object-contain" />
            ) : (
              <span className="text-xs text-gray-400">Tanda Tangan</span>
            )}
          </div>
          <p className="text-xs text-gray-600">Tanda Tangan</p>
        </div>
      </div>

      {/* Left Hand Fingerprints */}
      <div className="space-y-4">
        <h6 className="text-sm font-medium text-green-600">Sidik Jari</h6>
        <div className="space-y-4">
          <div>
            <h6 className="mb-3 text-sm font-medium">Tangan Kiri</h6>
            <div className="grid grid-cols-5 gap-4">
              <FingerPrintItem label="Ibu Jari" imageUrl={biometricData?.left_thumb} />
              <FingerPrintItem label="Telunjuk" imageUrl={biometricData?.left_index} />
              <FingerPrintItem label="Tengah" imageUrl={biometricData?.left_middle} />
              <FingerPrintItem label="Manis" imageUrl={biometricData?.left_ring} />
              <FingerPrintItem label="Kelingking" imageUrl={biometricData?.left_pinky} />
            </div>
          </div>

          {/* Right Hand Fingerprints */}
          <div>
            <h6 className="mb-3 text-sm font-medium">Tangan Kanan</h6>
            <div className="grid grid-cols-5 gap-4">
              <FingerPrintItem label="Ibu Jari" imageUrl={biometricData?.right_thumb} />
              <FingerPrintItem label="Telunjuk" imageUrl={biometricData?.right_index} />
              <FingerPrintItem label="Tengah" imageUrl={biometricData?.right_middle} />
              <FingerPrintItem label="Manis" imageUrl={biometricData?.right_ring} />
              <FingerPrintItem label="Kelingking" imageUrl={biometricData?.right_pinky} />
            </div>
          </div>
        </div>
      </div>

      {/* Palm Prints */}
      <div className="space-y-4">
        <h6 className="text-sm font-medium text-green-600">Telapak Tangan</h6>
        <div className="grid grid-cols-2 gap-8">
          <FingerPrintItem label="Telapak Kiri" imageUrl={biometricData?.left_palm} />
          <FingerPrintItem label="Telapak Kanan" imageUrl={biometricData?.right_palm} />
        </div>
      </div>

      {/* Iris */}
      <div className="space-y-4">
        <h6 className="text-sm font-medium text-green-600">Iris Mata</h6>
        <div className="grid grid-cols-2 gap-8">
          <FingerPrintItem label="Mata Kiri" imageUrl={biometricData?.left_iris} />
          <FingerPrintItem label="Mata Kanan" imageUrl={biometricData?.right_iris} />
        </div>
      </div>
    </section>
  );
};