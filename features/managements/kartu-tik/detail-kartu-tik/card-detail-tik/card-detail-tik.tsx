import { getSuspectDetail } from "@/services/form/form-get";
import { useQuery } from "@tanstack/react-query";
import { Card, Spinner } from "flowbite-react";
import { useParams } from "next/navigation";
import { InfoSection } from "./info-section";
import { BiometricSection } from "./biometric-section";
import dayjs from "dayjs";

export const CardDetailTik = () => {
  const { id } = useParams<{ id: string }>();
  const { data: detail, isLoading } = useQuery({
    queryKey: ["suspectDetail", id ?? ""],
    queryFn: () => getSuspectDetail(parseInt(id ?? "0", 10)),
  });

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Spinner />
      </div>
    );
  }

  const identity = detail?.suspect_identity;
  const history = detail?.suspect_history;
  const kejaksaan = detail?.kejaksaan_unit;
  const biometric = detail?.biometric_data;

  const identityList = [
    { label: "<PERSON><PERSON>", value: identity?.full_name },
    { label: "<PERSON><PERSON>", value: identity?.nickname },
    {
      label: "Tempat/Tgl Lahir",
      value:
        identity?.place_of_birth && identity?.date_of_birth
          ? `${identity.place_of_birth}, ${dayjs(identity.date_of_birth).format("DD-MM-YYYY")}`
          : null,
    },
    {
      label: "Jenis Kelamin",
      value:
        identity?.gender === "male"
          ? "Laki-laki"
          : identity?.gender === "female"
            ? "Perempuan"
            : null,
    },
    { label: "Bangsa/Suku", value: identity?.ethnic },
    { label: "Kewarganegaraan", value: identity?.citizenship?.toUpperCase() },
    { label: "Alamat", value: identity?.address },
    { label: "Nomor Identitas", value: identity?.identity_number },
    { label: "Agama", value: identity?.religion },
    { label: "Pekerjaan", value: identity?.job },
    { label: "Alamat Kantor", value: identity?.office_address },
    { label: "Status Perkawinan", value: identity?.marital_status },
    { label: "Organisasi Politik", value: identity?.organization_politic },
    { label: "Pendidikan", value: identity?.education },
    { label: "Residivis", value: identity?.is_recidivist ? "Ya" : "Tidak" },
  ];

  const historyList = [
    { label: "Pasal yang Dilanggar", value: history?.article_violated },
    { label: "Latar Belakang", value: history?.background_story },
    {
      label: "SP3/SKKP/RJ (No / Tgl)",
      value:
        history?.sp3_skkp && history?.sp3_skkp_date
          ? `${history.sp3_skkp} / ${dayjs(history.sp3_skkp_date).format("DD-MM-YYYY")}`
          : null,
    },
    { label: "Putusan PN", value: history?.court_decision_pn },
    { label: "Putusan MA", value: history?.court_decision_ma },
    { label: "Orang Tua/Saudara", value: history?.parent_name },
    { label: "Alamat Keluarga", value: history?.parent_description },
    { label: "Teman yang Dikenal", value: history?.friend_name },
    { label: "Lain-lain", value: history?.other },
  ];

  return (
    <Card className="border-main-2 space-y-8 p-8 shadow-none">
      {/* Header */}
      <header className="space-y-2 text-center">
        <h5 className="text-sm font-medium tracking-wide text-gray-700 uppercase">
          {kejaksaan?.name}
        </h5>
        <h6 className="text-lg font-bold underline decoration-2 underline-offset-4">
          KARTU TIK / TERSANGKA / TERDAKWA / TERPIDANA / DPO
        </h6>
        <p className="text-sm text-gray-600">
          Nomor: <span className="font-medium">{detail?.letter_code}</span>
        </p>
      </header>

      {/* Photo Section */}
      {identity?.photo_url && (
        <div className="flex justify-end">
          <div className="border-2 border-gray-300 p-2">
            <img
              src={identity.photo_url}
              alt="Foto Tersangka"
              className="h-32 w-24 object-cover"
            />
            <p className="mt-1 text-center text-xs text-gray-500">FOTO</p>
          </div>
        </div>
      )}

      {/* Identity Section */}
      <InfoSection title="I. IDENTITAS" data={identityList} />

      {/* History Section */}
      <InfoSection title="II. RIWAYAT PERKARA" data={historyList} />

      {/* Biometric Section */}
      <BiometricSection
        title="III. OTENTIKASI"
        biometricData={biometric}
        signature={identity?.signature_url}
      />
    </Card>
  );
};
