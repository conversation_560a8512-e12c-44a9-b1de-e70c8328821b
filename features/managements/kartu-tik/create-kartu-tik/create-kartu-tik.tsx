"use client";
import { PageHeader } from "@/components/ui/page-header/page-header";
import React from "react";
import {
  HiOutlineArrowLeft,
  HiOutlineDownload,
  HiOutlinePrinter,
} from "react-icons/hi";
import { FormKartuTik } from "../form";
import { useIdleConfirmation } from "@/lib/hooks/use-idle-confirmation";
import { signOut } from "@/utils/auth";

export const CreateKartuTik = () => {
  useIdleConfirmation({
    idleTimeout: 1 * 60 * 1000, // 1 minute of inactivity example
    warningTimeout: 60 * 1000, // 1 minute warning before logout
    onTimeout: signOut,
  });

  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        className="border-b border-gray-300 pb-6"
        title="Tambah Kartu TIK"
        backHref="/managements/form"
        backIcon={<HiOutlineArrowLeft className="h-5 w-5" />}
        rightElement={[
          {
            icon: <HiOutlinePrinter className="h-5 w-5" />,
            label: "Cetak Dokumen",
            isButton: true,
            onClick: () => console.log("Cetak"),
          },
          {
            icon: <HiOutlineDownload className="h-5 w-5" />,
            label: "Download",
            variant: "outline",
            isButton: true,
            onClick: () => console.log("Download"),
          },
        ]}
      />
      <FormKartuTik />
    </div>
  );
};
