import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import { routes } from "./lib/routes";

const SECURITY_HEADERS = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
  "Strict-Transport-Security": "max-age=63072000; includeSubDomains; preload",
};

const publicPaths = ["/auth/login", "/auth/forgot-password"];
// const kejariAccessPaths = [];
// const kejatiAccessPaths = [routes.managements.form.index, routes.managements.form.create, routes.managements.form.edit(""), routes.managements.form.view("")];
// const kejagungAccessPaths = ["/kejagung", "/dashboard", "/intel", "/management"];
// const intelKejariAccessPaths = ["/kejari", "/admin"];
// const intelKejatiAccessPaths = ["/kejati", "/admin"];
// const intelKejagungAccessPaths = ["/kejagung", "/admin"];

export async function middleware(request: NextRequest) {
  const { pathname, origin } = request.nextUrl;
  const token = await getToken({ req: request });
  const pathSegments = pathname.split("/").filter(Boolean);
  const firstSegment = pathSegments[0];

  if (token) {
    const role = token.user?.role as string;

    if (
      (pathname === "/" || firstSegment !== role) &&
      !publicPaths.includes(pathname)
    ) {
      const targetPath = pathname === "/" ? `/${role}` : `/${role}${pathname}`;
      return NextResponse.redirect(new URL(targetPath, origin));
    }

    if (firstSegment && publicPaths.includes(pathname)) {
      return NextResponse.redirect(new URL(`/${role}`, origin));
    }

    if (
      firstSegment &&
      firstSegment !== role &&
      !publicPaths.includes(pathname)
    ) {
      return new NextResponse("Not Found", { status: 404 });
    }
  } else {
    if (publicPaths.includes(pathname)) {
      return NextResponse.next();
    }

    const loginUrl = new URL(routes.login, origin);
    loginUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(loginUrl);
  }

  const response = NextResponse.next();
  Object.entries(SECURITY_HEADERS).forEach(([header, value]) => {
    response.headers.set(header, value);
  });

  return response;
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|assets|images).*)"],
};
