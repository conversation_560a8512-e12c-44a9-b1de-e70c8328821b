{"name": "flowbite-react-template-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier . --write", "format:check": "prettier . --check", "postinstall": "flowbite-react patch"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/lodash": "^4.17.20", "argon2": "^0.43.0", "axios": "^1.10.0", "axios-mock-adapter": "^2.1.0", "chartjs": "^0.3.24", "clsx": "^2.1.1", "dayjs": "^1.11.13", "flowbite-react": "^0.11.7", "lodash": "^4.17.21", "next": "^15.3.0", "next-auth": "^4.24.11", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-select": "^5.10.2", "react-signature-canvas": "^1.1.0-alpha.2", "zod": "^3.25.74", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^20.17.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.24.0", "eslint-config-next": "^15.3.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.4", "typescript": "^5.8.3"}}