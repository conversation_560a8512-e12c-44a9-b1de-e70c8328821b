{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.classAttributes": ["class", "className", "theme"], "tailwindCSS.experimental.classRegex": [["twMerge\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["createTheme(?:<\\w+>)?\\s*\\(([^)]*)\\)", "{?\\s?[\\w].*:\\s*?[\"'`]([^\"'`]*).*?,?\\s?}?"]], "typescript.tsdk": "node_modules/typescript/lib", "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}