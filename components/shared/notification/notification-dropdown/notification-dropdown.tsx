import { Badge, Dropdown, DropdownTheme } from "flowbite-react";
import { NotificationItemApproval } from "../../../ui/notification/notification-item";
import { ReactElement } from "react";
import { NotificationData } from "@/services/notification/notification-get.interface";
import { BiBell } from "react-icons/bi";
import { SkeletonUserProfile } from "@/components/ui/skeletons";

interface NotificationDropdownProps {
  label?: string;
  renderTrigger: (theme: DropdownTheme) => ReactElement;
  totalUnread?: number;
  data: NotificationData[];
  isLoading?: boolean;
}

export const NotificationDropdown = ({
  label = "",
  renderTrigger,
  totalUnread = 0,
  data,
  isLoading = false,
}: NotificationDropdownProps) => {
  return (
    <Dropdown
      label={label}
      dismissOnClick={false}
      renderTrigger={renderTrigger}
      className="w-full max-w-[400px] min-w-[300px] rounded-lg border border-gray-300 shadow-lg"
    >
      <div className="flex items-center justify-between border-b border-gray-300 bg-white p-4 shadow-md">
        <div className="text-dark flex items-center gap-2 text-base font-semibold">
          Notifikasi{" "}
          {totalUnread > 0 && (
            <Badge className="bg-primary-500 rounded-full text-white">
              {totalUnread}
            </Badge>
          )}
        </div>
        <div>
          <div className="text-primary-500 cursor-pointer text-xs font-medium hover:underline">
            Tandai semua telah dibaca
          </div>
        </div>
      </div>

      <div className="max-h-[450px] overflow-y-auto">
        {isLoading ? (
          Array.from({ length: 3 }).map((_, idx) => (
            <SkeletonUserProfile
              className="border-b border-gray-100"
              key={idx}
            />
          ))
        ) : data.length === 0 ? (
          <div className="flex flex-col items-center justify-center space-y-2 py-10 text-gray-400">
            <BiBell className="h-10 w-10" />
            <div className="text-sm">Tidak ada notifikasi</div>
          </div>
        ) : (
          data.map((item, index) => (
            <NotificationItemApproval
              key={index}
              data={item}
              onAccept={() => console.log("Accepted", item)}
              onReject={() => console.log("Rejected", item)}
            />
          ))
        )}
      </div>

      <div className="flex items-center justify-center border-t border-gray-200 bg-white px-4 py-3">
        <div className="text-primary-500 cursor-pointer text-sm hover:underline">
          Lihat semua notifikasi
        </div>
      </div>
    </Dropdown>
  );
};
