import { UserInfo } from "@/components/ui";
import { LuLogOut } from "react-icons/lu";

interface UserInfoTopbarProps {
  name: string;
  email: string;
  handleLogout: () => void;
}

export const UserInfoTopbar = ({
  name,
  email,
  handleLogout,
}: UserInfoTopbarProps) => {
  return (
    <div className="flex items-center gap-8 pl-4">
      <UserInfo name={name} email={email} />
      <div
        onClick={handleLogout}
        className="hover:text-primary-700 cursor-pointer rounded-full p-2 text-red-600 hover:bg-gray-100"
      >
        <LuLogOut size={18} />
      </div>
    </div>
  );
};
