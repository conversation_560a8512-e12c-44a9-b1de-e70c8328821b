import React, { useState, useEffect, useMemo, ChangeEvent } from "react";
import clsx from "clsx";
import { TextInput } from "flowbite-react";
import Image from "next/image";

import CountryCodeData from "@/assets/json/country-code.json";
import { PhoneInputProps } from "./phone-input-types";
import { ReactSelect } from "../react-select/react-select";
import { Option } from "../form-input/form-input-types";

const DEFAULT_COUNTRY_CODE = "+62";

export const PhoneInput: React.FC<PhoneInputProps> = ({
  id,
  name,
  className,
  disabled,
  onChange,
  // placeholder = "",
  required,
  value = "",
  isInvalid,
}) => {
  const [countryCode, setCountryCode] = useState<string | number>(
    DEFAULT_COUNTRY_CODE,
  );
  const [phoneNumber, setPhoneNumber] = useState<string | number>("");

  const countryOptions: Option[] = useMemo(() => {
    return CountryCodeData.map((country) => ({
      value: country.callingCode,
      label: `${country.flag}|${country.callingCode}`,
    }));
  }, []);

  useEffect(() => {
    const matchedCountry = CountryCodeData.find((c) =>
      value.startsWith(c.callingCode),
    );
    if (matchedCountry) {
      setCountryCode(matchedCountry.callingCode);
      setPhoneNumber(value.slice(matchedCountry.callingCode.length));
    } else {
      setPhoneNumber(value);
    }
  }, [value]);

  const triggerChange = (code: string | number, number: string | number) => {
    const newValue = `${code}${number}`;
    const syntheticEvent = {
      target: {
        name,
        value: newValue,
      },
    } as ChangeEvent<HTMLInputElement>;

    onChange?.(syntheticEvent);
  };

  const handleCountryChange = (selected: Option | null) => {
    const newCode = selected?.value || DEFAULT_COUNTRY_CODE;
    setCountryCode(newCode);
    triggerChange(newCode, phoneNumber);
  };

  const handlePhoneChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newNumber = e.target.value;
    setPhoneNumber(newNumber);
    triggerChange(countryCode, newNumber);
  };

  const selectedOption =
    countryOptions.find((opt) => opt.value === countryCode) || null;

  const formatOptionLabel = (
    option: Option,
    { context }: { context: "menu" | "value" },
  ) => {
    const [flag, code] = option.label.split("|");
    const isValue = context === "value";

    return (
      <div className="flex items-center gap-2">
        <Image
          src={flag}
          alt="Flag"
          width={isValue ? 25 : 20}
          height={isValue ? 25 : 20}
        />
        <span>{code}</span>
      </div>
    );
  };

  return (
    <TextInput
      id={id}
      name={name}
      value={phoneNumber}
      onChange={handlePhoneChange}
      // placeholder={placeholder}
      disabled={disabled}
      required={required}
      color={isInvalid ? "failure" : undefined}
      className={clsx("w-full", className)}
      theme={{
        field: {
          input: {
            base: "!pl-38",
          },
        },
      }}
      icon={() => (
        <ReactSelect
          options={countryOptions}
          value={selectedOption}
          onChange={(opt) => handleCountryChange(opt as Option)}
          formatOptionLabel={formatOptionLabel}
          customStyles={{
            control: () => ({
              minHeight: "44px",
              height: "44px",
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
            }),
          }}
        />
      )}
    />
  );
};
