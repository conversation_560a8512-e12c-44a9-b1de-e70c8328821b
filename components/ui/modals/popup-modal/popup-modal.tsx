"use client";

import { usePopupStore } from "@/stores/popup/popup";
import { <PERSON><PERSON>, Modal<PERSON>ody, ModalHeader } from "flowbite-react";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";
import { Button } from "@/components/ui/button";
import { HiOutlineExclamation } from "react-icons/hi";

export const PopupModal = () => {
  const {
    isOpen,
    closePopup,
    title,
    message,
    actions,
    icon: Icon,
    classNameContainerActions,
    withHeader,
    size,
  } = usePopupStore();

  if (!isOpen) {
    return null;
  }

  return (
    <Modal show={isOpen} size={size} onClose={closePopup} popup>
      {withHeader && <ModalHeader />}
      <ModalBody>
        <div
          className={twMerge("flex flex-col gap-8 pt-6", withHeader && "pt-0")}
        >
          <div className="flex">
            {Icon ? (
              Icon
            ) : (
              <div className="flex h-12 w-12 items-center justify-center rounded-full border-8 border-amber-100 bg-amber-200">
                <HiOutlineExclamation className="h-6 w-6 text-orange-500" />
              </div>
            )}
            <div className="space-y-2 px-4">
              <div className="m-0 text-lg font-bold text-gray-700">{title}</div>
              <div className="m-0 text-sm font-medium text-gray-400">
                {message}
              </div>
            </div>
          </div>
          <div
            className={twMerge(
              "flex w-full justify-center gap-2",
              classNameContainerActions,
            )}
          >
            {actions.map((action, index) => (
              <Button key={index} {...action}>
                {action.label}
              </Button>
            ))}
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};
