import React from "react";
import Select, { GroupBase } from "react-select";
import { StylesConfig } from "react-select";
import { ReactSelectTypes } from "./react-select.type";

export const ReactSelect = <
  Option,
  IsMulti extends boolean = false,
  Group extends GroupBase<Option> = GroupBase<Option>,
>({
  customStyles = {},
  ...props
}: ReactSelectTypes<Option, IsMulti, Group>) => {
  const defaultStyles: StylesConfig<Option, IsMulti, Group> = {
    placeholder: (provided, state) => ({
      ...provided,
      color: "#6B7280",
      fontSize: "14px",
      fontWeight: "500",
      ...(customStyles.placeholder?.(provided, state) ?? {}),
    }),
    control: (provided, state) => ({
      ...provided,
      minHeight: 40,
      borderRadius: 8,
      padding: "2px 8px",
      borderColor: "#D0D5DD",
      width: "100%",
      boxShadow: "0px 1px 2px 0px #1018280D",
      minWidth: "140px",
      ...(customStyles.control?.(provided, state) ?? {}),
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected ? "#efefef" : "#fff",
      color: "#2D3748",
      borderRadius: 8,
      padding: "8px 14px",
      fontWeight: "500",
      fontSize: "15px",
      "&:hover": {
        backgroundColor: "#efefef",
      },
      "&:active": {
        backgroundColor: "#efefef",
      },
      '&[data-selected="true"]': {
        backgroundColor: "#efefef",
      },
      ...(customStyles.option?.(provided, state) ?? {}),
    }),
    menu: (provided, state) => ({
      ...provided,
      padding: 4,
      zIndex: 20,
      borderRadius: 8,
      backgroundColor: "#fff",
      border: "1px solid #D0D5DD",
      boxShadow: "0 1px 8px 0 rgba(0, 0, 0, 0.2)",
      ...(customStyles.menu?.(provided, state) ?? {}),
    }),
    singleValue: (provided, state) => ({
      ...provided,
      ...(customStyles.singleValue?.(provided, state) ?? {}),
    }),
    multiValue: (provided, state) => ({
      ...provided,
      backgroundColor: "#e0e0e0",
      borderRadius: 6,
      ...(customStyles.multiValue?.(provided, state) ?? {}),
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
  };

  return <Select styles={defaultStyles} {...props} />;
};
