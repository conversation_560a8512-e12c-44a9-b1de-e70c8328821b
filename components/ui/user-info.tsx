import { AVATAR_DEFAULT_URL } from "@/constants";
import { Avatar } from "flowbite-react";
import React from "react";

interface UserInfoProps {
  name: string;
  email: string;
}

export const UserInfo = ({ name, email }: UserInfoProps) => {
  return (
    <div className="flex items-center gap-3">
      <Avatar alt="User settings" img={AVATAR_DEFAULT_URL(name)} rounded />
      <div className="">
        <div className="text-sm font-semibold text-gray-900">{name}</div>
        <div className="text-xs text-gray-500">{email}</div>
      </div>
    </div>
  );
};
