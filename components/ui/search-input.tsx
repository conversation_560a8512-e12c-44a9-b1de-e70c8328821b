"use client";
import { TextInput } from "flowbite-react";
import React from "react";
import { HiSearch } from "react-icons/hi";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";

export interface SearchInputProps {
  value?: string;
  placeholder?: string;
  className?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  placeholder = "Cari...",
  className = "",
  onChange,
}) => {
  return (
    <TextInput
      value={value}
      className={twMerge("min-w-[360px] rounded", className)}
      icon={() => (
        <div className="px-3">
          <HiSearch className="h-5 w-5" />
        </div>
      )}
      placeholder={placeholder}
      onChange={onChange}
    />
  );
};
