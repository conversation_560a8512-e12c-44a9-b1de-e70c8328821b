import { <PERSON><PERSON>, Button } from "flowbite-react";
import { NotificationItemApprovalProps } from "./notification-types";
import { NotificationStatusApproval } from "@/services/notification/notification-get.interface";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";
import { AVATAR_DEFAULT_URL } from "@/constants";
import { FiClock } from "react-icons/fi";

export const NotificationItemApproval = ({
  data,
  onAccept,
  onReject,
}: NotificationItemApprovalProps) => {
  return (
    <div
      className={twMerge(
        "flex items-start gap-2 border-b border-gray-300 p-4 hover:bg-gray-50",
        !data.is_read && "bg-gray-100 hover:bg-gray-200",
      )}
    >
      <Avatar
        alt="User"
        img={AVATAR_DEFAULT_URL(data.requested_by ?? "")}
        rounded
        className="!h-10 !w-16"
      />
      <div className="space-y-3">
        <div>
          <div className="text-sm font-semibold text-gray-900">
            {data.requested_by ?? ""}
          </div>
          <div className="text-xs text-gray-600">
            Request untuk edit form berikut:
          </div>
        </div>
        {/* <Button
          color="alternative"
          size="sm"
          className="border-primary-500 bordertext-left truncate bg-white text-xs shadow-[0_0_4px_rgba(70,52,52,0.15)]"
        >
          <BiFileBlank size={16} className="text-primary-500 mr-2" />
          {data.file_name || "-"}
        </Button> */}
        <div>
          <div className="text-xs font-semibold text-gray-900">Alasan:</div>
          <div className="text-xs text-gray-600">{data.note || "-"}</div>
        </div>
        {data.status === "pending" ? (
          <div className="flex items-center gap-2">
            <Button
              size="xs"
              className="border-0"
              color="primary"
              onClick={onAccept}
            >
              Terima
            </Button>
            <Button size="xs" color="alternative" onClick={onReject}>
              Tolak
            </Button>
          </div>
        ) : (
          <NotificationStatusBadge status={data.status} />
        )}
        <div className="text-xs font-medium text-gray-600">
          <FiClock className="mr-1 inline align-middle" />
          {data.received_ago || "-"}
        </div>
      </div>
    </div>
  );
};

export const NotificationStatusBadge = ({
  status,
}: {
  status: NotificationStatusApproval;
}) => {
  const statusClasses: Record<NotificationStatusApproval, string> = {
    pending: "",
    rejected: "bg-white text-red-600",
    approved: "bg-white text-primary-600",
  };
  const statusText: Record<NotificationStatusApproval, string> = {
    pending: "menunggu",
    rejected: "ditolak",
    approved: "diberikan",
  };
  return (
    <div
      className={`inline-block rounded-lg border px-3 py-2 text-sm font-medium ${statusClasses[status] ?? "bg-gray-100 text-gray-800"}`}
    >
      Akses edit {statusText[status] ?? "Tidak Diketahui"}
    </div>
  );
};
