/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useLayoutEffect,
} from "react";
import SignatureCanvas from "react-signature-canvas";
import { SignatureInputProps, SignatureInputRef } from "./types";
import clsx from "clsx";
import { Button } from "../button";

export const SignatureInput = forwardRef<
  SignatureInputRef,
  SignatureInputProps
>(
  (
    {
      id,
      value,
      isInvalid = false,
      onChange,
      className,
      disabled = false,
      errorMessage,
      width = "100%",
      height = 170,
      penColor = "black",
      velocityFilterWeight = 0.7,
      minWidth = 0.5,
      maxWidth = 2.5,
      minDistance = 2,
      dotSize,
      throttle = 16,
      backgroundColor = "rgba(0,0,0,0)",
      clearOnResize = true,
      onBegin,
      onEnd,
      clearButtonText = "Hapus",
      saveButtonText = "Simpan",
    },
    ref,
  ) => {
    const sigCanvas = useRef<SignatureCanvas>(null);
    const canvasContainerRef = useRef<HTMLDivElement>(null);
    const [signatureData, setSignatureData] = useState<string>("");
    const [isCanvasEmpty, setIsCanvasEmpty] = useState<boolean>(true);
    const [canvasWidth, setCanvasWidth] = useState<number>(0);

    // Handle responsive width and fixed height
    useLayoutEffect(() => {
      const updateCanvasSize = () => {
        if (canvasContainerRef.current && sigCanvas.current?.getCanvas()) {
          const containerWidth = canvasContainerRef.current.offsetWidth;
          setCanvasWidth(containerWidth);

          // Explicitly set canvas dimensions
          const canvas = sigCanvas.current.getCanvas();
          canvas.width = containerWidth;
          canvas.height = height;
        }
      };

      updateCanvasSize();

      const resizeObserver = new ResizeObserver(updateCanvasSize);
      if (canvasContainerRef.current) {
        resizeObserver.observe(canvasContainerRef.current);
      }

      return () => {
        resizeObserver.disconnect();
        window.removeEventListener("resize", updateCanvasSize);
      };
    }, [height]);

    const handleBegin = () => {
      setIsCanvasEmpty(false);
      if (onBegin) onBegin();
    };

    const handleEnd = () => {
      if (onEnd) onEnd();
    };

    useImperativeHandle(ref, () => ({
      clear: () => {
        sigCanvas.current?.clear();
        setSignatureData("");
        setIsCanvasEmpty(true);
        if (onChange) onChange("");
      },
      isEmpty: () => {
        return sigCanvas.current?.isEmpty() || false;
      },
      toDataURL: (type?: string, encoderOptions?: number) => {
        return sigCanvas.current?.toDataURL(type, encoderOptions) || "";
      },
      fromDataURL: (dataURL: string, options?: object) => {
        sigCanvas.current?.fromDataURL(dataURL, options);
        setSignatureData(dataURL);
        setIsCanvasEmpty(false);
      },
      toData: (): Array<
        Array<{ x: number; y: number; time: number; color: string }>
      > => {
        return (
          (sigCanvas.current?.toData() as Array<
            Array<{ x: number; y: number; time: number; color: string }>
          >) || []
        );
      },
      fromData: (
        pointGroups: Array<
          Array<{ x: number; y: number; time: number; color: string }>
        >,
      ) => {
        sigCanvas.current?.fromData(pointGroups as any);
        setIsCanvasEmpty(false);
      },
    }));

    const handleClear = () => {
      sigCanvas.current?.clear();
      setSignatureData("");
      setIsCanvasEmpty(true);
      if (onChange) onChange("");
    };

    const handleSave = () => {
      if (sigCanvas.current && !sigCanvas.current.isEmpty()) {
        const signatureDataURL = sigCanvas.current.toDataURL();
        setSignatureData(signatureDataURL);
        if (onChange) onChange(signatureDataURL);
      }
    };

    return (
      <div className="border-neutral-7 flex w-full flex-col gap-2 rounded-lg border p-4">
        <div id={id} className="flex w-full flex-col gap-2">
          <div
            ref={canvasContainerRef}
            className="relative"
            style={{
              width: width,
              height: `${height}px`,
              touchAction: "none",
            }}
          >
            <SignatureCanvas
              ref={sigCanvas}
              penColor={penColor}
              canvasProps={{
                width: canvasWidth,
                height: height,
                className: clsx(
                  "border border-neutral-7 rounded-lg w-full h-full",
                  isInvalid && "border-red-500",
                  disabled && "opacity-50 cursor-not-allowed",
                  className,
                ),
                style: {
                  touchAction: "none",
                },
              }}
              velocityFilterWeight={velocityFilterWeight}
              minWidth={minWidth}
              maxWidth={maxWidth}
              minDistance={minDistance}
              dotSize={dotSize}
              throttle={throttle}
              backgroundColor={backgroundColor}
              clearOnResize={clearOnResize}
              onBegin={handleBegin}
              onEnd={handleEnd}
            />
          </div>
          {isInvalid && errorMessage && (
            <p className="mt-1 text-sm text-red-500">{errorMessage}</p>
          )}
          <div className="mt-1 flex justify-end gap-2">
            <Button
              onClick={handleClear}
              disabled={disabled}
              className="border-red-500 text-red-500 hover:bg-red-50 focus:ring-red-500/50"
              variant="outline"
            >
              {clearButtonText}
            </Button>
            <Button onClick={handleSave} disabled={disabled || isCanvasEmpty}>
              {saveButtonText}
            </Button>
          </div>
        </div>
        {(signatureData || value) && (
          <div className="mt-4">
            <p className="mb-2 text-sm font-medium text-gray-700">
              Saved Signature:
            </p>
            <img
              src={signatureData || value}
              alt="Saved signature"
              className="max-w-full rounded-md border border-gray-300"
            />
          </div>
        )}
      </div>
    );
  },
);

SignatureInput.displayName = "SignatureInput";
