import { ReactNode } from "react";

export interface SignatureInputProps {
  id: string;
  name?: string;
  value?: string;
  required?: boolean;
  isInvalid?: boolean;
  onChange?: (dataURL: string) => void;
  className?: string;
  disabled?: boolean;
  errorMessage?: string;
  width?: number;
  height?: number;
  penColor?: string;
  velocityFilterWeight?: number;
  minWidth?: number;
  maxWidth?: number;
  minDistance?: number;
  dotSize?: number | (() => number);
  throttle?: number;
  backgroundColor?: string;
  clearOnResize?: boolean;
  onBegin?: () => void;
  onEnd?: () => void;
  label?: string | ReactNode;
  clearButtonText?: string;
  saveButtonText?: string;
}

export interface SignatureInputRef {
  clear: () => void;
  isEmpty: () => boolean;
  toDataURL: (type?: string, encoderOptions?: number) => string;
  fromDataURL: (dataURL: string, options?: object) => void;
  toData: () => Array<
    Array<{ x: number; y: number; time: number; color: string }>
  >;
  fromData: (
    pointGroups: Array<
      Array<{ x: number; y: number; time: number; color: string }>
    >,
  ) => void;
}
