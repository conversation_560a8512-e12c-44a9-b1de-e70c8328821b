"use client";

import { Button as <PERSON><PERSON><PERSON>low<PERSON><PERSON>, Spinner } from "flowbite-react";
import React, { forwardRef } from "react";
import { useRouter } from "next/navigation";
import { ButtonProps, ResponsiveSizeProp } from "./button-types";

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = "primary",
      size = "md",
      fullWidth = false,
      disabled = false,
      loading = false,
      leftIcon: LeftIcon,
      rightIcon: RightIcon,
      className = "",
      type = "button",
      onClick,
      href,
      replace = false,
      scroll = true,
      rel,
      ...rest
    },
    ref,
  ) => {
    const router = useRouter();

    const getVariantClass = () => {
      switch (variant) {
        case "primary":
          return "button-primary";
        case "secondary":
          return "bg-secondary-1 hover:bg-secondary-2 focus:ring-secondary-2/40 text-neutral-1";
        case "outline":
          return "border border-main-1 text-main-1 hover:bg-main-1/10 focus:ring-main-1/40 bg-transparent";
        case "text":
          return "bg-transparent text-main-1 hover:bg-main-1/10 focus:ring-0";
        case "gradient":
          return "bg-gradient-to-r from-main-1 to-main-3 hover:from-main-2 hover:to-main-4 text-white";
        default:
          return "button-primary";
      }
    };

    const sizeClassMap = {
      xs: "text-xs py-1 px-1.6",
      sm: "text-sm py-1.5 px-3",
      md: "py-2 px-4",
      lg: "text-lg py-2.5 px-5",
      xl: "text-xl py-3 px-6",
    };

    const getResponsiveSizeClass = (size: ResponsiveSizeProp = "md") => {
      if (typeof size === "string") {
        return sizeClassMap[size];
      }

      return Object.entries(size)
        .map(([breakpoint, sz]) => {
          const prefix = breakpoint === "base" ? "" : `${breakpoint}:`;
          return `${prefix}${sizeClassMap[sz]}`;
        })
        .join(" ");
    };

    const buttonClasses = [
      getVariantClass(),
      getResponsiveSizeClass(size),
      fullWidth ? "w-full" : "",
      "flex gap-2 cursor-pointer items-center justify-center rounded-lg transition-colors focus:ring-4",
      className,
    ]
      .filter(Boolean)
      .join(" ");

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (onClick) {
        onClick(event);
      }

      if (href && !event.defaultPrevented && !disabled && !loading) {
        event.preventDefault();
        if (replace) {
          router.replace(href, { scroll });
        } else {
          router.push(href, { scroll });
        }
      }
    };

    return (
      <ButtonFlowBite
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        type={type}
        onClick={handleClick}
        href={href}
        rel={rel}
        {...rest}
      >
        {loading && <Spinner className="mr-2 h-4 w-4" aria-label="Loading" />}
        {LeftIcon}
        {children}
        {RightIcon}
      </ButtonFlowBite>
    );
  },
);

Button.displayName = "Button";
