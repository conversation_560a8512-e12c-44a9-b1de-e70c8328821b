export type ResponsiveSize = "xs" | "sm" | "md" | "lg" | "xl";
export type ResponsiveSizeProp =
  | ResponsiveSize
  | Partial<Record<"base" | "sm" | "md" | "lg" | "xl", ResponsiveSize>>;

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: ResponsiveSizeProp;
  variant?: "primary" | "secondary" | "outline" | "text" | "gradient";
  fullWidth?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  href?: string;
  replace?: boolean;
  scroll?: boolean;
}
