"use client";
import { Doughn<PERSON> } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from "chart.js";

ChartJS.register(ArcElement, Tooltip, Legend);

interface DoughnutChartProps {
  data: {
    total: number;
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
      borderWidth?: number;
    }[];
  };
}

export const DoughnutChart: React.FC<DoughnutChartProps> = (props) => {
  const options = {
    cutout: "70%",
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          label: (tooltipItem: any) => {
            const label = props?.data?.labels[tooltipItem.dataIndex];
            const value = props?.data?.datasets[0].data[tooltipItem.dataIndex];
            return `${label}: ${value}%`;
          },
        },
      },
    },
  };

  return (
    <div className="flex flex-col rounded-2xl bg-white shadow-md">
      <div className="flex flex-row justify-between rounded-t-2xl bg-gray-100 p-6 pb-2">
        <h2 className="mb-4 text-xl font-semibold">Status Perkara</h2>
      </div>
      <div className="flex flex-col items-center gap-3 p-6 lg:flex-row">
        <div className="relative flex h-64 w-64 items-center justify-center">
          <Doughnut
            data={props.data}
            options={options}
            className="absolute z-10"
          />
          {/* Overlay for the center text */}
          <div className="absolute inset-0 z-0 flex flex-col items-center justify-center text-center">
            <span className="text-2xl font-bold text-green-600">
              {props.data.total}
            </span>
            <span className="text-xs text-gray-500">Perkara</span>
          </div>
        </div>
        <div className="ml-6 grid gap-4 space-y-2 text-sm md:grid-cols-2">
          {props?.data?.labels.map((label, index) => (
            <div className="flex items-center" key={index}>
              <span
                className="mr-2 inline-block h-3 w-3 rounded-full"
                style={{
                  backgroundColor:
                    props.data.datasets[0].backgroundColor[index],
                }}
              />
              <span className="w-24 text-gray-700">{label}</span>
              <span className="text-gray-500">
                {props.data.datasets[0].data[index]}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
