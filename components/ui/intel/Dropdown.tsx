"use client";
import { Dropdown, DropdownItem } from "flowbite-react";

interface DropdownProps {
  label: string;
  className?: string;
  arrowIcon?: boolean;
  disabled?: boolean;
  option: {
    label: string;
    value: string;
  }[];
  onClick?: (value: string) => void;
}

export const DropdownIntel: React.FC<DropdownProps> = (props) => {
  return (
    <Dropdown
      label={props.label || "Filter"}
      disabled={props.disabled}
      renderTrigger={() => (
        <button
          className={`${props.disabled && "cursor-not-allowed opacity-50"} flex items-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50`}
        >
          {props.label || "Filter"}
          {props.arrowIcon && (
            <svg
              className="ml-2 inline-block h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          )}
        </button>
      )}
      arrowIcon={props.arrowIcon}
    >
      {props.option &&
        props.option.map((item, index) => (
          <DropdownItem
            key={index}
            className="text-sm text-gray-700 hover:bg-gray-100"
            value={item.value}
            onClick={() => {
              if (props.onClick) {
                props.onClick(item.value);
              }
            }}
          >
            {item.label}
          </DropdownItem>
        ))}
    </Dropdown>
  );
};
