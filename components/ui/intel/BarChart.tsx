"use client";
import React from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
);

interface BarChartProps {
  data: {
    total: number;
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor: string[];
      borderColor?: string[];
      borderWidth?: number;
      borderRadius?: number;
    }[];
  };
}
export const BarChart: React.FC<BarChartProps> = (props) => {
  const options = {
    responsive: true,
    indexAxis: "y" as const,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: false,
        text: "Status Perkara (Total: 2800 Perkara)",
        font: {
          size: 16,
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: false,
        },
      },
      x: {
        display: false,
        grid: {
          display: false,
        },
      },
    },
  };

  return (
    <div className="rounded-2xl bg-white shadow-md">
      <div className="flex flex-row justify-between rounded-t-2xl border-b-gray-100 bg-gray-100 p-6 pb-2">
        <h2 className="mb-4 text-xl font-semibold">Status Perkara</h2>
        <h2 className="text-xl font-semibold text-[#007030]">
          {props.data.total}
          <span className="text-sm font-normal text-gray-500">Perkara</span>
        </h2>
      </div>
      <div className="max-h-80 px-4">
        <Bar data={props.data} options={options} />
      </div>
    </div>
  );
};
