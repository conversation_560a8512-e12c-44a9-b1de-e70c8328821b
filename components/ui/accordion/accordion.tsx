import {
  Accordion as AccordionFlowBite,
  AccordionContent,
  AccordionPanel,
  AccordionTitle,
} from "flowbite-react";
import React from "react";
import { AccordionProps } from "./accordion-types";

export const Accordion = ({ items }: AccordionProps) => {
  return (
    <AccordionFlowBite className="accordion" alwaysOpen>
      {items.map((item, index) => (
        <AccordionPanel
          className="accordion-panel"
          key={index}
          isOpen={item.isOpen}
        >
          <AccordionTitle>{item.title}</AccordionTitle>
          <AccordionContent className="accordion-content">
            {item.content}
          </AccordionContent>
        </AccordionPanel>
      ))}
    </AccordionFlowBite>
  );
};
