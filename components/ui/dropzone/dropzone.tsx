/* eslint-disable @typescript-eslint/no-unused-expressions */
import { FileInput, Modal, ModalBody } from "flowbite-react";
import { useState, useEffect, useRef, ChangeEvent, DragEvent } from "react";
import { DropzoneProps } from "./dropzone-types";
import clsx from "clsx";
import { HiOutlineEye, HiOutlineX } from "react-icons/hi";
import Image from "next/image";
import { Button } from "../button";

export const Dropzone: React.FC<DropzoneProps> = ({
  id,
  name,
  label,
  value,
  required,
  isInvalid,
  onChange,
  className,
  disabled,
  errorMessage,
  accept = "image/*",
  uploadText = "Click to upload or drag and drop",
  uploadDescription = "SVG, PNG, or JPG (MAX. 800x400px)",
}) => {
  const [preview, setPreview] = useState<string | null>(value || null);
  const [modalOpen, setModalOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setPreview(value || null);
  }, [value]);

  const triggerFileSelect = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const handleImagePreview = (
    file: File,
    originalEvent?: ChangeEvent<HTMLInputElement>,
  ) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      const result = reader.result as string;
      setPreview(result);

      if (onChange) {
        const syntheticEvent = {
          ...originalEvent,
          target: {
            ...originalEvent?.target,
            name: name || "",
            value: result,
            files: originalEvent?.target?.files || [file],
          },
        } as ChangeEvent<HTMLInputElement>;

        onChange(syntheticEvent);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file?.type.startsWith("image/")) {
      handleImagePreview(file, e);
    } else {
      onChange?.(e);
    }
  };

  const handleRemove = () => {
    setPreview(null);
    onChange?.({
      target: {
        name: name || "",
        value: "",
        files: null,
        type: "file",
        validity: { valid: true },
        validationMessage: "",
      },
    } as ChangeEvent<HTMLInputElement>);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (disabled) return;
    const file = e.dataTransfer.files?.[0];
    if (file?.type.startsWith("image/")) {
      handleImagePreview(file);
    }
    setIsDragging(false);
  };

  return (
    <>
      <Modal
        size="6xl"
        dismissible
        show={modalOpen}
        onClose={() => setModalOpen(false)}
      >
        <ModalBody className="flex flex-col items-center justify-center space-y-4 p-4">
          <div>
            <h3 className="text-primary-500 text-lg font-bold">
              {label || "Image Preview"}
            </h3>
          </div>
          <div className="h-full w-full overflow-auto">
            <Image
              src={preview || ""}
              alt={label || "Image preview"}
              className="h-[650px] w-full rounded-md object-contain"
              width={1100}
              height={400}
            />
          </div>
          <div>
            <Button
              variant="outline"
              color="gray.400"
              className="cursor-pointer border border-gray-300 bg-white hover:bg-gray-50"
              size="sm"
              onClick={() => setModalOpen(false)}
            >
              Kembali
            </Button>
          </div>
        </ModalBody>
      </Modal>
      <div className="flex w-full flex-col" data-testid={`dropzone-${id}`}>
        <div
          className={clsx(
            "flex w-full items-center justify-center",
            { "flex-col gap-2": preview },
            className,
          )}
        >
          <div
            className={clsx(
              "flex h-64 w-full cursor-pointer flex-col items-center justify-center rounded-lg border bg-white transition",
              isInvalid
                ? "border-red-500"
                : isDragging
                  ? "border-main-1 border-dashed"
                  : "border-neutral-7",
              !disabled ? "hover:bg-gray-50" : "cursor-not-allowed opacity-60",
            )}
            onClick={triggerFileSelect}
            onDragEnter={(e) => {
              e.preventDefault();
              !disabled && setIsDragging(true);
            }}
            onDragOver={(e) => {
              e.preventDefault();
              !disabled && setIsDragging(true);
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              setIsDragging(false);
            }}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center justify-center px-2 pt-5 pb-6 text-center">
              <div className="bg-neutral-15 border-neutral-16 flex h-10 w-10 items-center justify-center rounded-full border-4 p-1">
                <svg
                  className="h-4 w-4 text-[#475467]"
                  fill="none"
                  viewBox="0 0 20 16"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                  />
                </svg>
              </div>
              <p className="mb-2 text-sm text-gray-500">
                <span className="text-main-1 font-semibold">
                  {uploadText.split(" ")[0]}
                </span>{" "}
                {uploadText.split(" ").slice(1).join(" ")}
              </p>
              <p className="text-xs text-gray-500">{uploadDescription}</p>
            </div>
            <FileInput
              id={id}
              name={name}
              ref={fileInputRef}
              className="hidden"
              required={required}
              disabled={disabled}
              onChange={handleFileChange}
              aria-required={required}
              aria-invalid={isInvalid}
              accept={accept}
            />
          </div>

          {preview && (
            <div className="border-neutral-7 flex w-full items-center justify-between rounded-lg border px-2 py-2.5">
              <p className="m-0 px-2 text-sm font-medium">{label}</p>
              <div className="flex gap-2">
                <Button
                  variant="text"
                  className="h-6 w-6 p-0 hover:bg-gray-50"
                  onClick={() => setModalOpen(true)}
                  size="sm"
                >
                  <HiOutlineEye className="text-primary-500 h-6 w-6" />
                </Button>
                <Button
                  variant="text"
                  className="h-6 w-6 p-0 hover:bg-red-50"
                  size="sm"
                  onClick={handleRemove}
                >
                  <HiOutlineX className="h-6 w-6 text-red-500" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {isInvalid && errorMessage && (
          <p className="mt-1 text-sm text-red-500">{errorMessage}</p>
        )}
      </div>
    </>
  );
};
