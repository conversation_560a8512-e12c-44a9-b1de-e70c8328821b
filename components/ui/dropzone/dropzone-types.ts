export interface DropzoneProps {
  id: string;
  name?: string;
  label?: string;
  value?: string;
  required?: boolean;
  isInvalid?: boolean;
  onChange?: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | Date
    >,
  ) => void;
  className?: string;
  disabled?: boolean;
  errorMessage?: string;
  /**
   * Accepted file types, e.g., "image/*", ".jpg,.png", etc.
   */
  accept?: string;
  /**
   * Custom text to display in the dropzone
   */
  uploadText?: string;
  /**
   * Custom description text to display in the dropzone
   */
  uploadDescription?: string;
}
