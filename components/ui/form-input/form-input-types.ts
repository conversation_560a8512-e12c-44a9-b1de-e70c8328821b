import { GroupBase, Props as ReactSelectProps } from "react-select";

export type InputType =
  | "text"
  | "select"
  | "textarea"
  | "date"
  | "phone"
  | "radio"
  | "dropzone"
  | "signature"
  | "react-select";

export type Sizing = "sm" | "md" | "lg";

export interface Option {
  value: string | number;
  label: string;
}

type NativeChangeEvent =
  | React.ChangeEvent<HTMLInputElement>
  | React.ChangeEvent<HTMLTextAreaElement>
  | React.ChangeEvent<HTMLSelectElement>;

export type ReactSelectSingleValue = Option | null;

export type ReactSelectMultiValue = Option[];

export type ChangeHandler =
  | ((e: NativeChangeEvent | Date) => void)
  | ((option: ReactSelectSingleValue | ReactSelectMultiValue) => void);
export interface FormInputProps {
  id: string;
  name: string;

  inputType: InputType;
  type?: string;

  value?:
    | string
    | number
    | Date
    | ReactSelectSingleValue
    | ReactSelectMultiValue;
  onChange?: ChangeHandler;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;

  isInvalid?: boolean;
  isLoading?: boolean;
  errorMessage?: string;
  maxCharacters?: number;

  minDate?: Date;
  maxDate?: Date;
  dateFormat?: string;

  options?: Option[];

  label?: string;
  labelClassName?: string;
  className?: string;
  sizing?: Sizing;
  withRequiredStar?: boolean;
  leftElement?: React.ReactNode;
  rightElement?: React.ReactNode;

  reactSelectProps?: Partial<
    ReactSelectProps<Option, boolean, GroupBase<Option>>
  >;
}
