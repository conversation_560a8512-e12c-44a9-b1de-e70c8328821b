import { twMerge } from "flowbite-react/helpers/tailwind-merge";

interface SkeletonUserProfileProps {
  className?: string;
  classNameAvatar?: string;
  classNameText?: string;
  classNameTextSecondary?: string;
}

export const SkeletonUserProfile = ({
  className,
  classNameAvatar,
  classNameText,
  classNameTextSecondary,
}: SkeletonUserProfileProps) => {
  return (
    <div className={twMerge("flex animate-pulse gap-3 p-4", className)}>
      <div
        className={twMerge(
          "h-10 w-10 rounded-full bg-gray-200",
          classNameAvatar,
        )}
      />
      <div className="flex-1">
        <div
          className={twMerge(
            "mb-2 h-4 w-2/3 rounded bg-gray-200",
            classNameText,
          )}
        />
        <div
          className={twMerge(
            "h-3 w-1/2 rounded bg-gray-200",
            classNameTextSecondary,
          )}
        />
      </div>
    </div>
  );
};
