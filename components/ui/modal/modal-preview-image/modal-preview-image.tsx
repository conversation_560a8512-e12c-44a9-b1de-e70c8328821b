/* eslint-disable @next/next/no-img-element */
import React, { useEffect } from "react";
import { HiOutlineX } from "react-icons/hi";
import clsx from "clsx";
import { Button } from "@/components/ui/button";

interface ModalPreviewImageProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string | null;
  alt?: string;
  title?: string;
}

export const ModalPreviewImage: React.FC<ModalPreviewImageProps> = ({
  isOpen,
  onClose,
  imageUrl,
  title,
  alt = "Image preview",
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!isOpen || !imageUrl) return null;

  return (
    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative max-h-[90vh] max-w-[90vw] min-w-3xl overflow-hidden rounded-lg bg-white p-4 shadow-lg">
        <div
          className={clsx("flex", {
            "justify-between": title,
            "justify-end": !title,
          })}
        >
          {title && <h3 className="text-sm font-semibold">{title}</h3>}

          <Button
            variant="text"
            className="h-8 w-8 cursor-pointer rounded-full bg-white p-0 hover:bg-gray-50"
            size="sm"
            onClick={onClose}
          >
            <HiOutlineX className="h-6 w-6 text-black" />
          </Button>
        </div>
        <div className="flex h-full w-full items-center justify-center overflow-auto">
          <img
            src={imageUrl}
            alt={alt}
            className="max-h-[80vh] max-w-full object-contain"
          />
        </div>
      </div>
    </div>
  );
};
