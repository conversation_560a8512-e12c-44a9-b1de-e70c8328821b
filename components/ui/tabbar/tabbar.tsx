"use client";

import { TabItem, Tabs } from "flowbite-react";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";
import React from "react";
import { TabbarProps } from "./tabbar-types";

export const Tabbar: React.FC<TabbarProps> = ({
  items,
  className = "tabbar",
  variant = "underline",
  ariaLabel = "Tabs",
  onActiveTabChange,
}) => {
  const classes = twMerge("tabbar", className);
  return (
    <Tabs
      aria-label={ariaLabel}
      className={classes}
      variant={variant}
      onActiveTabChange={onActiveTabChange}
    >
      {items.map((item, index) => (
        <TabItem
          key={`tab-${index}`}
          active={item.active}
          title={item.title}
          icon={item.icon}
          disabled={item.disabled}
        >
          {item.content}
        </TabItem>
      ))}
    </Tabs>
  );
};
