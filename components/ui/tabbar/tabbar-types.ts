import { ReactNode } from "react";
import { IconType } from "react-icons";

export interface TabItemProps {
  title: string;
  icon?: IconType;
  content: ReactNode;
  active?: boolean;
  disabled?: boolean;
}

export interface TabbarProps {
  items: TabItemProps[];
  className?: string;
  variant?: "default" | "pills" | "underline" | "fullWidth";
  ariaLabel?: string;
  onActiveTabChange?: (activeTab: number) => void;
}
