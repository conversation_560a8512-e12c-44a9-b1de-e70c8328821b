import { Spinner } from "flowbite-react";
import React from "react";
import { useLoadingOverlayStore } from "@/stores/loading-overlay/loading-overlay";

export const LoadingOverlay = () => {
  const { isVisible, message, loaderComponent } = useLoadingOverlayStore();

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[99] flex flex-col items-center justify-center bg-white/70">
      {loaderComponent || <Spinner className="border-main-1" />}
      <p>{message || "Loading..."}</p>
    </div>
  );
};
