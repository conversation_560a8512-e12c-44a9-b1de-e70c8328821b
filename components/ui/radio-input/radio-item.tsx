import React from 'react'
import { RadioItemProps } from './types'
import clsx from 'clsx'

export const RadioItem: React.FC<RadioItemProps> = ({
    id,
    label,
    value,
    name,
    checked,
    disabled,
    onChange
}) => {
    return (
        <div className="flex items-center">
            <input 
                id={id} 
                type="radio" 
                value={value} 
                name={name} 
                checked={checked}
                disabled={disabled}
                onChange={onChange}
                className={clsx(
                    'w-4 h-4 text-main-1 bg-gray-100 checked:bg-main-1 border-gray-300 focus:ring-t-main-1 focus:ring-2',
                    disabled && 'opacity-50 cursor-not-allowed'
                )} 
            />
            <label htmlFor={id} className={clsx(
                'ms-2 text-sm font-medium',
                disabled && 'opacity-50 cursor-not-allowed'
            )}>{label}</label>
        </div>
    )
}
