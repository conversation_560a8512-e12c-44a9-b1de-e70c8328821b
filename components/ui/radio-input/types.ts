export interface RadioOption {
  label: string;
  value: string;
}

export interface RadioInputProps {
  id: string;
  name?: string;
  placeholder?: string;
  value?: string;
  required?: boolean;
  isInvalid?: boolean;
  onChange?: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | Date
    >,
  ) => void;
  className?: string;
  disabled?: boolean;
  options?: RadioOption[];
}

export interface RadioItemProps {
  id: string;
  value?: string;
  label?: string;
  name?: string;
  checked?: boolean;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}
