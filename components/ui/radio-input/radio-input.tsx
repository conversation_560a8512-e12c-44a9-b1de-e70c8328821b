import React from "react";
import { RadioItem } from "./radio-item";
import { RadioInputProps } from "./types";
import clsx from "clsx";
import { DROPDOWN_YA_TIDAK } from "@/constants";

export const RadioInput: React.FC<RadioInputProps> = ({
  id,
  name,
  value,
  isInvalid,
  onChange,
  className,
  disabled,
  required,
  options = DROPDOWN_YA_TIDAK,
}) => {
  return (
    <div
      id={id}
      className={clsx(
        "flex flex-wrap gap-4 rounded-lg border px-4 py-2.5",
        isInvalid ? "border-red-500" : "border-neutral-7",
        className,
      )}
      aria-required={required}
    >
      {options.map((item, index) => (
        <RadioItem
          key={index}
          id={`${id}-${item.value}`}
          label={item.label}
          value={item.value}
          name={name}
          checked={value === item.value}
          disabled={disabled}
          onChange={onChange}
        />
      ))}
    </div>
  );
};
