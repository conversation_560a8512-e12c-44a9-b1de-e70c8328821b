"use client";

import React, { Suspense } from "react";
import { useSession } from "next-auth/react";
import { TopBar } from "@/components/shared/topbar";
import Image from "next/image";
import Logo from "@/assets/logo.png";
import { APP_NAME } from "@/constants";

const LoadingFallback = () => (
  <div className="flex h-screen items-center justify-center text-gray-600">
    <div className="flex flex-col items-center space-y-2">
      <Image src={Logo} height={100} width={100} alt="Logo Kejaksaan" />
      <div className="text-primary-300 text-base font-bold">{APP_NAME}</div>
    </div>
  </div>
);

const SessionGate = ({ children }: { children: React.ReactNode }) => {
  const { status } = useSession({ required: true });
  if (status === "loading") {
    return <LoadingFallback />;
  }

  return <>{children}</>;
};

export const DashboardLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <SessionGate>
        <div className="w-full bg-neutral-100">
          <TopBar />
          <div className="container-fluid mx-auto min-h-screen w-full pt-28 pb-24">
            {children}
          </div>
        </div>
      </SessionGate>
    </Suspense>
  );
};

export default DashboardLayout;
