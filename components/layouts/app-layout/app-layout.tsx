"use client";
import { customTheme } from "@/themes/custom-theme";
import { ThemeConfig, ThemeProvider } from "flowbite-react";
import { SessionProvider } from "next-auth/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { PopupModal } from "@/components/ui/modals/popup-modal/popup-modal";
import { LoadingOverlay } from "@/components/ui/loading-overlay";

const queryClient = new QueryClient();

export const AppLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <SessionProvider>
      <ThemeConfig dark={false} />
      <ThemeProvider theme={customTheme}>
        <QueryClientProvider client={queryClient}>
          {children}
          <PopupModal />
          <LoadingOverlay />
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </ThemeProvider>
    </SessionProvider>
  );
};
export default AppLayout;
