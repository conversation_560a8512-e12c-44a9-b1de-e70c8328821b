@import "tailwindcss";
@plugin "flowbite-react/plugin/tailwindcss";
@source "../.flowbite-react/class-list.json";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --color-main-1: #007030;
  --color-main-2: #2c7a67;
  --color-main-3: #0f725e;
  --color-main-4: #154733;
  --color-main-5: #104735;
  --color-main-6: #cfe3df;
  --color-secondary-1: #f3c73e;
  --color-secondary-2: #fee123;
  --color-secondary-3: #e2e11b;
  --color-neutral-1: #000000;
  --color-neutral-2: #ffffff;
  --color-neutral-3: #4d5859;
  --color-neutral-4: #a2aaad;
  --color-neutral-5: #d8dcda;
  --color-neutral-6: #fafafa;
  --color-neutral-7: #d0d5dd;
  --color-neutral-8: #344054;
  --color-neutral-9: #b0b0b0;
  --color-neutral-10: #e0e0e0;
  --color-neutral-11: #1d1b20;
  --color-neutral-12: #49454f;
  --color-neutral-13: #d9d9d9;
  --color-neutral-14: #757575;
  --color-neutral-15: #f2f4f7;
  --color-neutral-16: #f9fafb;
  --color-neutral-17: #eaecf0;
}

* {
  font-family: "Inter", sans-serif;
}
body {
  @apply text-neutral-8 bg-white;
}

hr {
  @apply text-neutral-10;
}

.tabbar > div[role="tablist"] {
  @apply bg-main-6 px-6;
}

.tabbar > div[role="tablist"] > button {
  @apply p-3;
}
.tabbar > div[role="tablist"] > button[aria-selected="true"] {
  @apply text-neutral-11 border-main-2;
}
.tabbar > div[role="tablist"] > button[aria-selected="false"]:disabled {
  @apply text-neutral-12/50;
}
.tabbar > div[role="tablist"] > button[aria-selected="false"]:hover {
  @apply border-main-2/40;
}

.button-primary {
  @apply bg-main-1 hover:bg-main-2 focus:ring-main-2/40 text-white;
}

.accordion {
  @apply border-none;
}
.accordion > button {
  @apply bg-main-6 border-neutral-13 focus:ring-main-2/40 cursor-pointer rounded-lg border border-t border-r border-l px-6 py-3;
}

.accordion > button > h2 {
  @apply text-main-3 text-sm font-bold;
}
.accordion > button > svg {
  @apply text-main-3;
}
.accordion > button:first {
  @apply mt-0;
}
.accordion > button {
  @apply mt-2;
}
.accordion-panel {
  @apply border-neutral-13;
}

.accordion-content {
  @apply border-neutral-13 mb-2 rounded-b-lg border border-t-0 border-r border-b border-l;
}

/* form input */
.tik-number {
  @apply !w-[80px];
}
.tik-number > div > input {
  @apply border-neutral-7 rounded-none border-t-0 border-r-0 border-b-2 border-l-0;
}
.ref-number {
  @apply inline !w-fit pb-0;
}
.ref-number > div > input {
  @apply !h-auto border-none px-1 py-0 focus:ring-0;
}

.phone-input > div > div > select,
.phone-input > div > div > input {
  @apply border-none bg-white;
}
.phone-input > div > div > select {
  @apply focus:border-none focus:ring-0 focus:outline-none;
}
.phone-input > div > div > input {
  @apply w-full px-0 focus:border-none focus:ring-0 focus:outline-none;
}

.input-dropdown {
  @apply focus:ring-main-2/40 border-neutral-7 text-neutral-8 disabled:text-neutral-9 border bg-white;
}

.input-dropdown > ul {
  @apply rounded-xl bg-white py-2;
}

.input-dropdown > ul > li > button {
  @apply text-neutral-8 py-2 hover:bg-gray-200;
}

.container-fluid {
  @apply max-w-[1440px] px-8;
}

.search > div > input {
  @apply rounded-r-none;
}

@theme {
  --color-primary-300: #2c7a67;
  --color-primary-400: #0f725e;
  --color-primary-500: #007030;
  --color-primary-600: #154733;
  --color-primary-700: #104735;

  --color-secondary-300: #e2e11b;
  --color-secondary-500: #fee123;
  --color-secondary-600: #f3c73e;

  --color-neutral-50: #fafafa;
  --color-neutral-100: #fcfcfd;
  --color-neutral-200: #d8dcda;
  --color-neutral-300: #a2aaad;
  --color-neutral-600: #4d5859;
}
