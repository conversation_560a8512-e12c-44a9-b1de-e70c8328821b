import { Dashboard } from "@/features/dashboards";
import { authOptions } from "@/lib/auth/auth-options";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function Page() {
  const sessionData = await getServerSession(authOptions);

  if (sessionData?.user?.role !== "intel") {
    return redirect("/managements/form");
  }

  return <Dashboard userData={sessionData?.user} />;
}
