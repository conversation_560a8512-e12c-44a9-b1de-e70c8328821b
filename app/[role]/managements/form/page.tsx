import React from "react";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth-options";
import { ManagementForm } from "@/features/managements/management-form";

export const metadata: Metadata = {
  title: "Form Management | Dashboard Kejaksaan",
};

const Page = async () => {
  const sessionData = await getServerSession(authOptions);
  return <ManagementForm userData={sessionData?.user} />;
};

export default Page;
