import Image from "next/image";
import Logo from "@/assets/logo.png";
import { LoginForm } from "@/features/auth";
import { Metadata } from "next";
import { APP_NAME } from "@/constants";

export const metadata: Metadata = {
  title: "Login Dashboard Kejaksaan",
  description: "Halaman Login Kejaksaan",
};

export default function LoginPage() {
  return (
    <div className="flex w-full max-w-md flex-col space-y-5 rounded-lg bg-white p-8 shadow">
      <div className="flex flex-col items-center justify-center space-y-2 text-center">
        <Image src={Logo} height={100} width={100} alt="Logo Kejaksaan" />
        <div className="text-primary-300 text-lg font-bold">{APP_NAME}</div>
      </div>
      <LoginForm />
    </div>
  );
}
