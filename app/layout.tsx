import { ThemeConfig, ThemeModeScript } from "flowbite-react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import AppLayout from "@/components/layouts/app-layout";

const interSans = Inter({
  variable: "--font-inter-sans",
  subsets: ["latin"],
});


export const metadata: Metadata = {
  title: "Dashboard Kejaksaan",
  description: "Dashboard Kejaksaan",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <ThemeModeScript />
      </head>
      <body
        className={`${interSans.variable} antialiased`}
      >
        <ThemeConfig dark={false} />
        <AppLayout>{children}</AppLayout>
      </body>
    </html>
  );
}
