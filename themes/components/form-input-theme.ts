import { theme } from "flowbite-react";
import { twMerge } from "flowbite-react/helpers/tailwind-merge";

export const formInputTheme = {
  textInput: {
    field: {
      base: "relative w-full",
      icon: {
        base: twMerge(
          theme.textInput.field.icon.base,
          "pointer-events-auto pl-0",
        ),
      },
      rightIcon: {
        base: twMerge(
          theme.textInput.field.rightIcon.base,
          "pointer-events-auto pr-0",
        ),
      },
      input: {
        base: twMerge(
          theme.textInput.field.input.base,
          "block w-full border !bg-white focus:ring-1 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",
        ),
        sizes: {
          md: twMerge(
            theme.textInput.field.input.sizes.md,
            "h-[45px] px-4 py-2.5 text-sm font-medium",
          ),
        },
      },
    },
  },
  select: {
    field: {
      base: "relative w-full",
      select: {
        base: twMerge(
          theme.select.field.select.base,
          "block w-full border !bg-white focus:ring-1 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",
        ),
        sizes: {
          md: twMerge(
            theme.select.field.select.sizes.md,
            "h-[45px] px-4 py-2.5 text-sm font-medium",
          ),
        },
      },
    },
  },
  textarea: {
    base: twMerge(
      theme.textarea.base,
      "block w-full border !bg-white focus:ring-1 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",
    ),
  },
};

export type FormInputTheme = typeof formInputTheme;
