export const paginationTheme = {
  pagination: {
    base: "flex flex-row justify-between items-center",
    layout: {
      table: {
        base: "text-sm text-gray-700",
        span: "font-semibold text-gray-900",
      },
    },
    pages: {
      base: "mt-0 inline-flex items-center -space-x-px",
      showIcon: "inline-flex",
      previous: {
        base: "ml-0 rounded-l-lg border border-gray-300 bg-neutral-16 px-3 py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700  cursor-pointer disabled:cursor-not-allowed",
        icon: "h-5 w-5",
      },
      next: {
        base: "rounded-r-lg border border-gray-300 bg-neutral-16 px-3 py-2 leading-tight cursor-pointer disabled:cursor-not-allowed",
        icon: "h-5 w-5",
      },
      selector: {
        base: "w-12 border border-gray-300 bg-white py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700  cursor-pointer",
        active:
          "bg-neutral-16 text-gray-700 hover:bg-cyan-100 hover:text-cyan-700 ",
        disabled: "cursor-not-allowed opacity-50",
      },
    },
  },
};

export type PaginationTheme = typeof paginationTheme;
